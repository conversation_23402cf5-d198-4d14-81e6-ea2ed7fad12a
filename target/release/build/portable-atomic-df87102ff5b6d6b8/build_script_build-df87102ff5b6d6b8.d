/Users/<USER>/Desktop/minimal_agent/target/release/build/portable-atomic-df87102ff5b6d6b8/build_script_build-df87102ff5b6d6b8: /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/portable-atomic-1.11.1/build.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/portable-atomic-1.11.1/version.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/portable-atomic-1.11.1/src/gen/build.rs

/Users/<USER>/Desktop/minimal_agent/target/release/build/portable-atomic-df87102ff5b6d6b8/build_script_build-df87102ff5b6d6b8.d: /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/portable-atomic-1.11.1/build.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/portable-atomic-1.11.1/version.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/portable-atomic-1.11.1/src/gen/build.rs

/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/portable-atomic-1.11.1/build.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/portable-atomic-1.11.1/version.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/portable-atomic-1.11.1/src/gen/build.rs:

# env-dep:CARGO_PKG_NAME=portable-atomic
