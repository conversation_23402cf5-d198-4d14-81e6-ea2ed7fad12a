{"rustc": 15497389221046826682, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 1369601567987815722, "path": 2502868820896097467, "deps": [[5103565458935487, "futures_io", false, 14057000815696921458], [40386456601120721, "percent_encoding", false, 17053723009873957405], [530211389790465181, "hex", false, 18101160191250436895], [788558663644978524, "crossbeam_queue", false, 214653621067931623], [966925859616469517, "ahash", false, 15350343242718931156], [1162433738665300155, "crc", false, 1361697496555235823], [1464803193346256239, "event_listener", false, 8924006104150257413], [1811549171721445101, "futures_channel", false, 996144516689291247], [3129130049864710036, "memchr", false, 7330605642944994284], [3150220818285335163, "url", false, 1194369935816037867], [3405817021026194662, "hashlink", false, 8777775473663429833], [3646857438214563691, "futures_intrusive", false, 6911047262194481236], [3666196340704888985, "smallvec", false, 15202094782772080800], [3712811570531045576, "byteorder", false, 8457400611973759127], [3722963349756955755, "once_cell", false, 7614613593811702135], [5986029879202738730, "log", false, 14673404771220805342], [7620660491849607393, "futures_core", false, 2036499553877758907], [8008191657135824715, "thiserror", false, 3977842506883530111], [8319709847752024821, "uuid", false, 14056565851186426354], [8606274917505247608, "tracing", false, 225064485727770721], [9538054652646069845, "tokio", false, 3571645822981772478], [9689903380558560274, "serde", false, 955524774400141567], [9857275760291862238, "sha2", false, 7597572666114532680], [9897246384292347999, "chrono", false, 14296117713350680749], [10629569228670356391, "futures_util", false, 11522432202009255299], [10862088793507253106, "sqlformat", false, 1913538873746521391], [11295624341523567602, "rustls", false, 5349469556434592196], [12170264697963848012, "either", false, 16350567872461886267], [14483812548788871374, "indexmap", false, 4475018913282907760], [15367738274754116744, "serde_json", false, 14346182443888027274], [16066129441945555748, "bytes", false, 6258224254247018143], [16311359161338405624, "rustls_pemfile", false, 13684426654220822995], [16973251432615581304, "tokio_stream", false, 6706160229127433383], [17106256174509013259, "atoi", false, 5488871492379381216], [17605717126308396068, "paste", false, 2532808228993607689], [17652733826348741533, "webpki_roots", false, 12427669171126780661]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/sqlx-core-7a2c7384fe929c1f/dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}