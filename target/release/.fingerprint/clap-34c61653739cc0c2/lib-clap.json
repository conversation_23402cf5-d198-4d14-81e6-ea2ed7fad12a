{"rustc": 15497389221046826682, "features": "[\"color\", \"default\", \"derive\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 9656904095642909417, "path": 15674423206804831573, "deps": [[4925398738524877221, "clap_derive", false, 5492095980013561871], [14814905555676593471, "clap_builder", false, 5662164884499739838]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/clap-34c61653739cc0c2/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}