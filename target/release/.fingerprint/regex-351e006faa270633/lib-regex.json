{"rustc": 15497389221046826682, "features": "[\"std\", \"unicode-case\", \"unicode-perl\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 2040997289075261528, "path": 17677806421990553393, "deps": [[555019317135488525, "regex_automata", false, 16561924072775870422], [9408802513701742484, "regex_syntax", false, 17414479722118736369]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/regex-351e006faa270633/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}