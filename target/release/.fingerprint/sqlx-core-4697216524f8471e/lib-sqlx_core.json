{"rustc": 15497389221046826682, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 2040997289075261528, "path": 2502868820896097467, "deps": [[5103565458935487, "futures_io", false, 15064183570235429268], [40386456601120721, "percent_encoding", false, 18116733225190186316], [530211389790465181, "hex", false, 13419665068198382217], [788558663644978524, "crossbeam_queue", false, 18215176502446871825], [966925859616469517, "ahash", false, 10803558914041514482], [1162433738665300155, "crc", false, 13991608612428750816], [1464803193346256239, "event_listener", false, 2525144241849905589], [1811549171721445101, "futures_channel", false, 18118111336737393194], [3129130049864710036, "memchr", false, 13166971342116605409], [3150220818285335163, "url", false, 17783435957512046636], [3405817021026194662, "hashlink", false, 8087327046641099740], [3646857438214563691, "futures_intrusive", false, 6466445124787589212], [3666196340704888985, "smallvec", false, 15638043916080713937], [3712811570531045576, "byteorder", false, 9211783425946892004], [3722963349756955755, "once_cell", false, 16954947227637799546], [5986029879202738730, "log", false, 15299173955770377330], [7620660491849607393, "futures_core", false, 13628575546603588870], [8008191657135824715, "thiserror", false, 4211153654984506220], [8319709847752024821, "uuid", false, 1189778669656923886], [8606274917505247608, "tracing", false, 3228759391539871072], [9538054652646069845, "tokio", false, 10471197303684864000], [9689903380558560274, "serde", false, 4658247129376000094], [9857275760291862238, "sha2", false, 801987486150431303], [9897246384292347999, "chrono", false, 17659023986160429836], [10629569228670356391, "futures_util", false, 419867197800294054], [10862088793507253106, "sqlformat", false, 9163050732972687926], [11295624341523567602, "rustls", false, 12403447438045468276], [12170264697963848012, "either", false, 16884818029437409958], [14483812548788871374, "indexmap", false, 13829970459989645522], [15367738274754116744, "serde_json", false, 14136571128365680171], [16066129441945555748, "bytes", false, 5483846833092552874], [16311359161338405624, "rustls_pemfile", false, 2414451583343594557], [16973251432615581304, "tokio_stream", false, 9136932163263046788], [17106256174509013259, "atoi", false, 8334502490988551744], [17605717126308396068, "paste", false, 2532808228993607689], [17652733826348741533, "webpki_roots", false, 13311370916043125593]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/sqlx-core-4697216524f8471e/dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}