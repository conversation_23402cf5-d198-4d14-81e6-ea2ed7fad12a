{"rustc": 15497389221046826682, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"chrono\", \"default\", \"json\", \"migrate\", \"sqlite\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"time\", \"uuid\"]", "target": 13494433325021527976, "profile": 1369601567987815722, "path": 5918130507351860430, "deps": [[996810380461694889, "sqlx_core", false, 7438196129722551778], [2713742371683562785, "syn", false, 8564696810326308693], [3060637413840920116, "proc_macro2", false, 6474605767069777252], [15733334431800349573, "sqlx_macros_core", false, 11227614898333496904], [17990358020177143287, "quote", false, 4938454037897622923]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/sqlx-macros-20ab7d2c92060acc/dep-lib-sqlx_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}