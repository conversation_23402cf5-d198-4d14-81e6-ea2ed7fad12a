{"rustc": 15497389221046826682, "features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 17342157952639649116, "path": 10639462379859935832, "deps": [[1213098572879462490, "json5_rs", false, 9023411791327347645], [1965680986145237447, "yaml_rust2", false, 12049482590254374151], [2244620803250265856, "ron", false, 16345580976759275390], [6502365400774175331, "nom", false, 1476880092920903136], [6517602928339163454, "path<PERSON><PERSON>", false, 3105566974231498686], [9689903380558560274, "serde", false, 4658247129376000094], [11946729385090170470, "async_trait", false, 6603749685665971300], [13475460906694513802, "convert_case", false, 17701646940558288571], [14618892375165583068, "ini", false, 17292472451484611539], [15367738274754116744, "serde_json", false, 14136571128365680171], [15609422047640926750, "toml", false, 8550438441198373154]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/config-a52f6cf6675a10a3/dep-lib-config", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}