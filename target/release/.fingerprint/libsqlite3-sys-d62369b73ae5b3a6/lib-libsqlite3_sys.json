{"rustc": 15497389221046826682, "features": "[\"bundled\", \"bundled_bindings\", \"cc\", \"pkg-config\", \"unlock_notify\", \"vcpkg\"]", "declared_features": "[\"bindgen\", \"buildtime_bindgen\", \"bundled\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"bundled_bindings\", \"cc\", \"default\", \"in_gecko\", \"loadable_extension\", \"min_sqlite_version_3_14_0\", \"openssl-sys\", \"pkg-config\", \"prettyplease\", \"preupdate_hook\", \"quote\", \"session\", \"sqlcipher\", \"syn\", \"unlock_notify\", \"vcpkg\", \"wasm32-wasi-vfs\", \"winsqlite3\", \"with-asan\"]", "target": 3421942236757206917, "profile": 2040997289075261528, "path": 16843773626851331136, "deps": [[11564048663401192931, "build_script_build", false, 12140902249385051282]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/libsqlite3-sys-d62369b73ae5b3a6/dep-lib-libsqlite3_sys", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}