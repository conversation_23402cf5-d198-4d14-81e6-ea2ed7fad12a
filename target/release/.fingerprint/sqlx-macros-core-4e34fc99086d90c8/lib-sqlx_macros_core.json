{"rustc": 15497389221046826682, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"chrono\", \"default\", \"json\", \"migrate\", \"sqlite\", \"sqlx-sqlite\", \"tokio\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tokio\", \"uuid\"]", "target": 961973412475639632, "profile": 1369601567987815722, "path": 14732888299092582741, "deps": [[530211389790465181, "hex", false, 18101160191250436895], [996810380461694889, "sqlx_core", false, 7438196129722551778], [1441306149310335789, "tempfile", false, 17307956730549451588], [2713742371683562785, "syn", false, 8564696810326308693], [3060637413840920116, "proc_macro2", false, 6474605767069777252], [3150220818285335163, "url", false, 1194369935816037867], [3405707034081185165, "dotenvy", false, 4712033267777720887], [3722963349756955755, "once_cell", false, 7614613593811702135], [8045585743974080694, "heck", false, 15347697357617215180], [9538054652646069845, "tokio", false, 3571645822981772478], [9689903380558560274, "serde", false, 955524774400141567], [9857275760291862238, "sha2", false, 7597572666114532680], [11838249260056359578, "sqlx_sqlite", false, 4658901039376847167], [12170264697963848012, "either", false, 16350567872461886267], [15367738274754116744, "serde_json", false, 14346182443888027274], [17990358020177143287, "quote", false, 4938454037897622923]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/sqlx-macros-core-4e34fc99086d90c8/dep-lib-sqlx_macros_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}