{"rustc": 15497389221046826682, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 18348216721672176038, "path": 2520728235853731257, "deps": [[5103565458935487, "futures_io", false, 15064183570235429268], [1811549171721445101, "futures_channel", false, 18118111336737393194], [7013762810557009322, "futures_sink", false, 17659448170356258935], [7620660491849607393, "futures_core", false, 13628575546603588870], [10629569228670356391, "futures_util", false, 419867197800294054], [12779779637805422465, "futures_executor", false, 3622575324839058796], [16240732885093539806, "futures_task", false, 14691489045382319464]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/futures-f06750cbf02d7a43/dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}