{"rustc": 15497389221046826682, "features": "[\"barrier\", \"default\", \"lazy\", \"lock_api\", \"lock_api_crate\", \"mutex\", \"once\", \"rwlock\", \"spin_mutex\"]", "declared_features": "[\"barrier\", \"default\", \"fair_mutex\", \"lazy\", \"lock_api\", \"lock_api_crate\", \"mutex\", \"once\", \"portable-atomic\", \"portable_atomic\", \"rwlock\", \"spin_mutex\", \"std\", \"ticket_mutex\", \"use_ticket_mutex\"]", "target": 4260413527236709406, "profile": 5347358027863023418, "path": 999763129322981317, "deps": [[8081351675046095464, "lock_api_crate", false, 10974051990238987876]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spin-4da4b88df81e8f80/dep-lib-spin", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}