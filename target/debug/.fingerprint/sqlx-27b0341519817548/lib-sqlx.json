{"rustc": 15497389221046826682, "features": "[\"_rt-tokio\", \"any\", \"chrono\", \"default\", \"json\", \"macros\", \"migrate\", \"runtime-tokio\", \"runtime-tokio-rustls\", \"sqlite\", \"sqlx-macros\", \"sqlx-sqlite\", \"tls-rustls\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_unstable-all-types\", \"all-databases\", \"any\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"regexp\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sqlite\", \"sqlx-macros\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tls-native-tls\", \"tls-none\", \"tls-rustls\", \"uuid\"]", "target": 3003836824758849296, "profile": 5347358027863023418, "path": 5418147681317881287, "deps": [[228475551920078470, "sqlx_macros", false, 7914310316836269588], [996810380461694889, "sqlx_core", false, 16518972819124908937], [11838249260056359578, "sqlx_sqlite", false, 11830325383818703389]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-27b0341519817548/dep-lib-sqlx", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}