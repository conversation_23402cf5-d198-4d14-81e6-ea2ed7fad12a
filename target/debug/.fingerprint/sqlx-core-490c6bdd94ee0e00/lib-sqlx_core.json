{"rustc": 15497389221046826682, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 8276155916380437441, "path": 2502868820896097467, "deps": [[5103565458935487, "futures_io", false, 11713653420324842350], [40386456601120721, "percent_encoding", false, 3962760285878548712], [530211389790465181, "hex", false, 3162717946525821415], [788558663644978524, "crossbeam_queue", false, 13491543447786632578], [966925859616469517, "ahash", false, 2975252280203616724], [1162433738665300155, "crc", false, 2230813563554487634], [1464803193346256239, "event_listener", false, 2701671814515992154], [1811549171721445101, "futures_channel", false, 608922185914070194], [3129130049864710036, "memchr", false, 16565091858469226673], [3150220818285335163, "url", false, 6427377658131192631], [3405817021026194662, "hashlink", false, 11639284585779945520], [3646857438214563691, "futures_intrusive", false, 4464378954276552295], [3666196340704888985, "smallvec", false, 8314014758584946959], [3712811570531045576, "byteorder", false, 10202456893722128079], [3722963349756955755, "once_cell", false, 16880676189629333222], [5986029879202738730, "log", false, 747140028569255248], [7620660491849607393, "futures_core", false, 989016875435182914], [8008191657135824715, "thiserror", false, 699432480210165696], [8319709847752024821, "uuid", false, 6594912127446851391], [8606274917505247608, "tracing", false, 6411137347337608784], [9538054652646069845, "tokio", false, 4843374485105757900], [9689903380558560274, "serde", false, 1225241228224930150], [9857275760291862238, "sha2", false, 2515193325936343574], [9897246384292347999, "chrono", false, 14323161010863920922], [10629569228670356391, "futures_util", false, 991853205098301936], [10862088793507253106, "sqlformat", false, 8061517159847985159], [11295624341523567602, "rustls", false, 17942188089193204378], [12170264697963848012, "either", false, 14629360592067002268], [14483812548788871374, "indexmap", false, 13833185280983922386], [15367738274754116744, "serde_json", false, 7450017850861889772], [16066129441945555748, "bytes", false, 4662682899331477856], [16311359161338405624, "rustls_pemfile", false, 18011380875588757999], [16973251432615581304, "tokio_stream", false, 17050591438719031279], [17106256174509013259, "atoi", false, 2540349757327770498], [17605717126308396068, "paste", false, 12442323560985218848], [17652733826348741533, "webpki_roots", false, 3034505989899000772]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-core-490c6bdd94ee0e00/dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}