{"rustc": 15497389221046826682, "features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 18330098564635666122, "path": 10639462379859935832, "deps": [[1213098572879462490, "json5_rs", false, 6490920601661837771], [1965680986145237447, "yaml_rust2", false, 14116284614699419609], [2244620803250265856, "ron", false, 18279276779754263942], [6502365400774175331, "nom", false, 3055100526333866024], [6517602928339163454, "path<PERSON><PERSON>", false, 14381377889498126830], [9689903380558560274, "serde", false, 17345789778403161651], [11946729385090170470, "async_trait", false, 2786551838605296543], [13475460906694513802, "convert_case", false, 1783313618194761437], [14618892375165583068, "ini", false, 5356150597133189275], [15367738274754116744, "serde_json", false, 5138447124364986451], [15609422047640926750, "toml", false, 17744536480437679030]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/config-b02ea8abef480743/dep-lib-config", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}