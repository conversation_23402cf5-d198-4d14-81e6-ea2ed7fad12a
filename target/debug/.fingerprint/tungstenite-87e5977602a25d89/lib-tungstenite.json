{"rustc": 15497389221046826682, "features": "[\"data-encoding\", \"handshake\", \"http\", \"httparse\", \"sha1\", \"url\"]", "declared_features": "[\"__rustls-tls\", \"data-encoding\", \"default\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"sha1\", \"url\", \"webpki-roots\"]", "target": 1270341572213479472, "profile": 5347358027863023418, "path": 10264102243517035732, "deps": [[99287295355353247, "data_encoding", false, 3743992536920346390], [3150220818285335163, "url", false, 16228039935699487194], [3712811570531045576, "byteorder", false, 14003771949845975574], [4359956005902820838, "utf8", false, 2207908133266290980], [5986029879202738730, "log", false, 15418844225888363990], [6163892036024256188, "httparse", false, 7951590712849512294], [8008191657135824715, "thiserror", false, 15129313711590934046], [9010263965687315507, "http", false, 10164169568142337572], [10724389056617919257, "sha1", false, 14311926762437545076], [13208667028893622512, "rand", false, 17197256484956262508], [16066129441945555748, "bytes", false, 2202256488003570352]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tungstenite-87e5977602a25d89/dep-lib-tungstenite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}