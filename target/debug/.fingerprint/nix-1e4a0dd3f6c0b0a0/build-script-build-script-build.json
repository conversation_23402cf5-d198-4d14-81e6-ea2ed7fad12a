{"rustc": 15497389221046826682, "features": "[\"fs\", \"ioctl\", \"poll\", \"process\", \"signal\", \"term\"]", "declared_features": "[\"acct\", \"aio\", \"default\", \"dir\", \"env\", \"event\", \"fanotify\", \"feature\", \"fs\", \"hostname\", \"inotify\", \"ioctl\", \"kmod\", \"memoffset\", \"mman\", \"mount\", \"mqueue\", \"net\", \"personality\", \"pin-utils\", \"poll\", \"process\", \"pthread\", \"ptrace\", \"quota\", \"reboot\", \"resource\", \"sched\", \"signal\", \"socket\", \"term\", \"time\", \"ucontext\", \"uio\", \"user\", \"zerocopy\"]", "target": 5408242616063297496, "profile": 3033921117576893, "path": 12370436728073740267, "deps": [[13650835054453599687, "cfg_aliases", false, 6968608773125024667]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/nix-1e4a0dd3f6c0b0a0/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}