{"rustc": 15497389221046826682, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 3992724396554112236, "path": 14758062964366250590, "deps": [[1009387600818341822, "matchers", false, 9899804354516697938], [1017461770342116999, "sharded_slab", false, 10744920197318988349], [3424551429995674438, "tracing_core", false, 6714492897436325971], [3666196340704888985, "smallvec", false, 17028069649303187059], [3722963349756955755, "once_cell", false, 16880676189629333222], [8606274917505247608, "tracing", false, 6411137347337608784], [8614575489689151157, "nu_ansi_term", false, 13351804225321127168], [9451456094439810778, "regex", false, 3355483251257877655], [10806489435541507125, "tracing_log", false, 12774390482849732845], [12427285511609802057, "thread_local", false, 6768548167021349612]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-subscriber-1d6fa57258f6778f/dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}