{"rustc": 15497389221046826682, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 5347358027863023418, "path": 2502868820896097467, "deps": [[5103565458935487, "futures_io", false, 8057485613747405845], [40386456601120721, "percent_encoding", false, 10923069874993409885], [530211389790465181, "hex", false, 15716712176986183320], [788558663644978524, "crossbeam_queue", false, 3429175205295871089], [966925859616469517, "ahash", false, 217462639499057041], [1162433738665300155, "crc", false, 12354376841376766554], [1464803193346256239, "event_listener", false, 16097707524098402354], [1811549171721445101, "futures_channel", false, 3197674648440762696], [3129130049864710036, "memchr", false, 2453238714601280815], [3150220818285335163, "url", false, 14597469335269964030], [3405817021026194662, "hashlink", false, 16966332551334576298], [3646857438214563691, "futures_intrusive", false, 7162726145668633577], [3666196340704888985, "smallvec", false, 8618377089856328319], [3712811570531045576, "byteorder", false, 12528720280681778099], [3722963349756955755, "once_cell", false, 9473742205910936843], [5986029879202738730, "log", false, 15418844225888363990], [7620660491849607393, "futures_core", false, 741080699306893542], [8008191657135824715, "thiserror", false, 15129313711590934046], [8319709847752024821, "uuid", false, 4389974993469258379], [8606274917505247608, "tracing", false, 6092767106357085505], [9538054652646069845, "tokio", false, 6675511175951087997], [9689903380558560274, "serde", false, 17345789778403161651], [9857275760291862238, "sha2", false, 5730933689170393201], [9897246384292347999, "chrono", false, 860613703694241909], [10629569228670356391, "futures_util", false, 8082628522907223237], [10862088793507253106, "sqlformat", false, 14829441474000771344], [11295624341523567602, "rustls", false, 4185552031432217287], [12170264697963848012, "either", false, 9365813778036965095], [14483812548788871374, "indexmap", false, 5098653512580021890], [15367738274754116744, "serde_json", false, 5138447124364986451], [16066129441945555748, "bytes", false, 2202256488003570352], [16311359161338405624, "rustls_pemfile", false, 6676640667109371002], [16973251432615581304, "tokio_stream", false, 13301665057445719247], [17106256174509013259, "atoi", false, 8267452899864799811], [17605717126308396068, "paste", false, 12442323560985218848], [17652733826348741533, "webpki_roots", false, 14641004795720409963]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-core-5b341862bb3c5447/dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}