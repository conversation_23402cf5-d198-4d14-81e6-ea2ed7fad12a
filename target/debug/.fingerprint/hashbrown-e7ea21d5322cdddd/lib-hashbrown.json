{"rustc": 15497389221046826682, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 5347358027863023418, "path": 2806833925311483133, "deps": [[966925859616469517, "ahash", false, 217462639499057041], [9150530836556604396, "allocator_api2", false, 13527296752974687236]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-e7ea21d5322cdddd/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}