{"rustc": 15497389221046826682, "features": "[\"data-encoding\", \"handshake\", \"http\", \"httparse\", \"sha1\", \"url\"]", "declared_features": "[\"__rustls-tls\", \"data-encoding\", \"default\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"sha1\", \"url\", \"webpki-roots\"]", "target": 1270341572213479472, "profile": 8276155916380437441, "path": 10264102243517035732, "deps": [[99287295355353247, "data_encoding", false, 10351193223675486849], [3150220818285335163, "url", false, 12189824531421008010], [3712811570531045576, "byteorder", false, 10202456893722128079], [4359956005902820838, "utf8", false, 6705295949727221661], [5986029879202738730, "log", false, 747140028569255248], [6163892036024256188, "httparse", false, 6943756945488163657], [8008191657135824715, "thiserror", false, 699432480210165696], [9010263965687315507, "http", false, 547080602106306486], [10724389056617919257, "sha1", false, 1089853287949232565], [13208667028893622512, "rand", false, 9955401510086412533], [16066129441945555748, "bytes", false, 4662682899331477856]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tungstenite-5a4b819ff94b8d70/dep-lib-tungstenite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}