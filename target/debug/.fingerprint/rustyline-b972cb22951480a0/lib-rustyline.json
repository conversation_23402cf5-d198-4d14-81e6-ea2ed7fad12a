{"rustc": 15497389221046826682, "features": "[\"custom-bindings\", \"default\", \"fd-lock\", \"home\", \"radix_trie\", \"with-dirs\", \"with-file-history\"]", "declared_features": "[\"buffer-redux\", \"case_insensitive_history_search\", \"custom-bindings\", \"default\", \"derive\", \"fd-lock\", \"home\", \"radix_trie\", \"regex\", \"rusqlite\", \"rustyline-derive\", \"signal-hook\", \"skim\", \"termios\", \"with-dirs\", \"with-file-history\", \"with-fuzzy\", \"with-sqlite-history\"]", "target": 485391283985806857, "profile": 5347358027863023418, "path": 10680007337893423799, "deps": [[1232198224951696867, "unicode_segmentation", false, 9509149559458298434], [1322514204948454048, "unicode_width", false, 4394474778017261732], [2828590642173593838, "cfg_if", false, 12338924958951320774], [2924422107542798392, "libc", false, 8132220066861334924], [3129130049864710036, "memchr", false, 2453238714601280815], [4544379658388519060, "home", false, 14413950523972140080], [5986029879202738730, "log", false, 15418844225888363990], [7896293946984509699, "bitflags", false, 225395297921550519], [8655257597193238683, "nix", false, 6612937584893785597], [9520952519707787197, "fd_lock", false, 12929318143801601398], [13495677209339419690, "radix_trie", false, 1111762491843384676], [17716308468579268865, "utf8parse", false, 1647235081905062454]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustyline-b972cb22951480a0/dep-lib-rustyline", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}