{"rustc": 15497389221046826682, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"chrono\", \"default\", \"json\", \"migrate\", \"sqlite\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"time\", \"uuid\"]", "target": 13494433325021527976, "profile": 3033921117576893, "path": 5918130507351860430, "deps": [[996810380461694889, "sqlx_core", false, 17522003529136778714], [2713742371683562785, "syn", false, 198541313443917174], [3060637413840920116, "proc_macro2", false, 3764910832982512469], [15733334431800349573, "sqlx_macros_core", false, 11602005493579698760], [17990358020177143287, "quote", false, 14172411166005396569]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-macros-da2a55e2fd490070/dep-lib-sqlx_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}