{"rustc": 15497389221046826682, "features": "[\"custom-bindings\", \"default\", \"fd-lock\", \"home\", \"radix_trie\", \"with-dirs\", \"with-file-history\"]", "declared_features": "[\"buffer-redux\", \"case_insensitive_history_search\", \"custom-bindings\", \"default\", \"derive\", \"fd-lock\", \"home\", \"radix_trie\", \"regex\", \"rusqlite\", \"rustyline-derive\", \"signal-hook\", \"skim\", \"termios\", \"with-dirs\", \"with-file-history\", \"with-fuzzy\", \"with-sqlite-history\"]", "target": 485391283985806857, "profile": 8276155916380437441, "path": 10680007337893423799, "deps": [[1232198224951696867, "unicode_segmentation", false, 6977319477826123109], [1322514204948454048, "unicode_width", false, 6569582497504545725], [2828590642173593838, "cfg_if", false, 14606369845840734082], [2924422107542798392, "libc", false, 3973217962427861888], [3129130049864710036, "memchr", false, 16565091858469226673], [4544379658388519060, "home", false, 12621913477656504645], [5986029879202738730, "log", false, 747140028569255248], [7896293946984509699, "bitflags", false, 5929743009486456269], [8655257597193238683, "nix", false, 18033591993902982463], [9520952519707787197, "fd_lock", false, 1878940408371564183], [13495677209339419690, "radix_trie", false, 2004737289308656006], [17716308468579268865, "utf8parse", false, 2772707747853632357]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustyline-0be31bf58290847c/dep-lib-rustyline", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}