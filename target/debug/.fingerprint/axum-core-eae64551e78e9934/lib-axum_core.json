{"rustc": 15497389221046826682, "features": "[\"tracing\"]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 5347358027863023418, "path": 6847758121185959536, "deps": [[784494742817713399, "tower_service", false, 9055238356565759915], [1906322745568073236, "pin_project_lite", false, 1083680141490467697], [2517136641825875337, "sync_wrapper", false, 8309689088888449910], [7712452662827335977, "tower_layer", false, 11081322074278188224], [7858942147296547339, "rustversion", false, 7320769263156569191], [8606274917505247608, "tracing", false, 6092767106357085505], [9010263965687315507, "http", false, 10164169568142337572], [10229185211513642314, "mime", false, 15868005461723149675], [10629569228670356391, "futures_util", false, 8082628522907223237], [11946729385090170470, "async_trait", false, 2786551838605296543], [14084095096285906100, "http_body", false, 12737559600396859314], [16066129441945555748, "bytes", false, 2202256488003570352], [16900715236047033623, "http_body_util", false, 4940155728570076930]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-core-eae64551e78e9934/dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}