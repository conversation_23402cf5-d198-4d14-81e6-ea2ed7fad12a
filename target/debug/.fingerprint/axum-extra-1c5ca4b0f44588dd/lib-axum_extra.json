{"rustc": 15497389221046826682, "features": "[\"default\", \"multipart\", \"tracing\", \"typed-header\"]", "declared_features": "[\"async-read-body\", \"attachment\", \"cookie\", \"cookie-key-expansion\", \"cookie-private\", \"cookie-signed\", \"default\", \"erased-json\", \"form\", \"json-deserializer\", \"json-lines\", \"multipart\", \"protobuf\", \"query\", \"tracing\", \"typed-header\", \"typed-routing\"]", "target": 4770478002602207591, "profile": 8276155916380437441, "path": 14129476245043374171, "deps": [[784494742817713399, "tower_service", false, 13435136064593454444], [1906322745568073236, "pin_project_lite", false, 112714291231645796], [4359148418957042248, "axum_core", false, 14431197877546628303], [4891297352905791595, "axum", false, 8420885441669501503], [5695049318159433696, "tower", false, 13914687725264876008], [7435852374066785895, "headers", false, 6464935531955454174], [7712452662827335977, "tower_layer", false, 4590867070091174697], [9010263965687315507, "http", false, 547080602106306486], [9689903380558560274, "serde", false, 1225241228224930150], [10229185211513642314, "mime", false, 1038738364931238327], [10629569228670356391, "futures_util", false, 991853205098301936], [12285238697122577036, "fastrand", false, 15875379163955306208], [12757619235593077227, "multer", false, 12284386146946415080], [14084095096285906100, "http_body", false, 15105246052722349515], [16066129441945555748, "bytes", false, 4662682899331477856], [16900715236047033623, "http_body_util", false, 6130199079153330386]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-extra-1c5ca4b0f44588dd/dep-lib-axum_extra", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}