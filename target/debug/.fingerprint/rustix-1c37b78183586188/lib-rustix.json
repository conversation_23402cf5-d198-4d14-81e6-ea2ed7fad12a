{"rustc": 15497389221046826682, "features": "[\"alloc\", \"default\", \"fs\", \"libc-extra-traits\", \"std\", \"use-libc-auxv\"]", "declared_features": "[\"all-apis\", \"alloc\", \"cc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"itoa\", \"libc\", \"libc-extra-traits\", \"libc_errno\", \"linux_4_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"once_cell\", \"param\", \"pipe\", \"process\", \"procfs\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 11375657722578241393, "path": 116566192017369991, "deps": [[2924422107542798392, "libc", false, 8132220066861334924], [3430646239657634944, "build_script_build", false, 3592398744491095631], [7896293946984509699, "bitflags", false, 225395297921550519], [14633813869673313769, "libc_errno", false, 15259914061832379695]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-1c37b78183586188/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}