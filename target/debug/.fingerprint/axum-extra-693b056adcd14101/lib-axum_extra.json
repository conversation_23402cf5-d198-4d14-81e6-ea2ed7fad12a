{"rustc": 15497389221046826682, "features": "[\"default\", \"multipart\", \"tracing\", \"typed-header\"]", "declared_features": "[\"async-read-body\", \"attachment\", \"cookie\", \"cookie-key-expansion\", \"cookie-private\", \"cookie-signed\", \"default\", \"erased-json\", \"form\", \"json-deserializer\", \"json-lines\", \"multipart\", \"protobuf\", \"query\", \"tracing\", \"typed-header\", \"typed-routing\"]", "target": 4770478002602207591, "profile": 5347358027863023418, "path": 14129476245043374171, "deps": [[784494742817713399, "tower_service", false, 9055238356565759915], [1906322745568073236, "pin_project_lite", false, 1083680141490467697], [4359148418957042248, "axum_core", false, 4470855892612675092], [4891297352905791595, "axum", false, 2574819712800486831], [5695049318159433696, "tower", false, 6806045929354133722], [7435852374066785895, "headers", false, 15890794315476218584], [7712452662827335977, "tower_layer", false, 11081322074278188224], [9010263965687315507, "http", false, 10164169568142337572], [9689903380558560274, "serde", false, 17345789778403161651], [10229185211513642314, "mime", false, 15868005461723149675], [10629569228670356391, "futures_util", false, 8082628522907223237], [12285238697122577036, "fastrand", false, 13145915849068543476], [12757619235593077227, "multer", false, 10556243301938924899], [14084095096285906100, "http_body", false, *************6859314], [16066129441945555748, "bytes", false, 2202256488003570352], [16900715236047033623, "http_body_util", false, 4940155728570076930]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-extra-693b056adcd14101/dep-lib-axum_extra", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}