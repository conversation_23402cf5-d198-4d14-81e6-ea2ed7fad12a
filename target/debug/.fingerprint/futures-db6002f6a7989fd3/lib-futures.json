{"rustc": 15497389221046826682, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 17669703692130904899, "path": 2520728235853731257, "deps": [[5103565458935487, "futures_io", false, 8057485613747405845], [1811549171721445101, "futures_channel", false, 3197674648440762696], [7013762810557009322, "futures_sink", false, 6686448209423272027], [7620660491849607393, "futures_core", false, 741080699306893542], [10629569228670356391, "futures_util", false, 8082628522907223237], [12779779637805422465, "futures_executor", false, 988885192177398502], [16240732885093539806, "futures_task", false, 10283026207924098611]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-db6002f6a7989fd3/dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}