{"rustc": 15497389221046826682, "features": "[\"fs\", \"ioctl\", \"poll\", \"process\", \"signal\", \"term\"]", "declared_features": "[\"acct\", \"aio\", \"default\", \"dir\", \"env\", \"event\", \"fanotify\", \"feature\", \"fs\", \"hostname\", \"inotify\", \"ioctl\", \"kmod\", \"memoffset\", \"mman\", \"mount\", \"mqueue\", \"net\", \"personality\", \"pin-utils\", \"poll\", \"process\", \"pthread\", \"ptrace\", \"quota\", \"reboot\", \"resource\", \"sched\", \"signal\", \"socket\", \"term\", \"time\", \"ucontext\", \"uio\", \"user\", \"zerocopy\"]", "target": 2594889627657062481, "profile": 5347358027863023418, "path": 2987354484067030061, "deps": [[2828590642173593838, "cfg_if", false, 12338924958951320774], [2924422107542798392, "libc", false, 8132220066861334924], [7896293946984509699, "bitflags", false, 225395297921550519], [8655257597193238683, "build_script_build", false, 146535604032049637]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/nix-634f4e7ea9ad08a7/dep-lib-nix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}