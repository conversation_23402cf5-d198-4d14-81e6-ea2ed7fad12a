{"$message_type":"diagnostic","message":"unused import: `unicode_width::UnicodeWidthStr`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/cli/input.rs","byte_start":45,"byte_end":75,"line_start":2,"line_end":2,"column_start":5,"column_end":35,"is_primary":true,"text":[{"text":"use unicode_width::UnicodeWidthStr;","highlight_start":5,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/cli/input.rs","byte_start":41,"byte_end":77,"line_start":2,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use unicode_width::UnicodeWidthStr;","highlight_start":1,"highlight_end":36},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `unicode_width::UnicodeWidthStr`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli/input.rs:2:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse unicode_width::UnicodeWidthStr;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct takes 2 generic arguments but 1 generic argument was supplied","code":{"code":"E0107","explanation":"An incorrect number of generic arguments was provided.\n\nErroneous code example:\n\n```compile_fail,E0107\nstruct Foo<T> { x: T }\n\nstruct Bar { x: Foo }             // error: wrong number of type arguments:\n                                  //        expected 1, found 0\nstruct Baz<S, T> { x: Foo<S, T> } // error: wrong number of type arguments:\n                                  //        expected 1, found 2\n\nfn foo<T, U>(x: T, y: U) {}\nfn f() {}\n\nfn main() {\n    let x: bool = true;\n    foo::<bool>(x);                 // error: wrong number of type arguments:\n                                    //        expected 2, found 1\n    foo::<bool, i32, i32>(x, 2, 4); // error: wrong number of type arguments:\n                                    //        expected 2, found 3\n    f::<'static>();                 // error: wrong number of lifetime arguments\n                                    //        expected 0, found 1\n}\n```\n\nWhen using/declaring an item with generic arguments, you must provide the exact\nsame number:\n\n```\nstruct Foo<T> { x: T }\n\nstruct Bar<T> { x: Foo<T> }               // ok!\nstruct Baz<S, T> { x: Foo<S>, y: Foo<T> } // ok!\n\nfn foo<T, U>(x: T, y: U) {}\nfn f() {}\n\nfn main() {\n    let x: bool = true;\n    foo::<bool, u32>(x, 12);              // ok!\n    f();                                  // ok!\n}\n```\n"},"level":"error","spans":[{"file_name":"src/cli/input.rs","byte_start":116,"byte_end":122,"line_start":5,"line_end":5,"column_start":13,"column_end":19,"is_primary":true,"text":[{"text":"    editor: Editor<()>,","highlight_start":13,"highlight_end":19}],"label":"expected 2 generic arguments","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/cli/input.rs","byte_start":123,"byte_end":125,"line_start":5,"line_end":5,"column_start":20,"column_end":22,"is_primary":false,"text":[{"text":"    editor: Editor<()>,","highlight_start":20,"highlight_end":22}],"label":"supplied 1 generic argument","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"struct defined here, with 2 generic parameters: `H`, `I`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rustyline-12.0.0/src/lib.rs","byte_start":19446,"byte_end":19447,"line_start":587,"line_end":587,"column_start":19,"column_end":20,"is_primary":false,"text":[{"text":"pub struct Editor<H: Helper, I: History> {","highlight_start":19,"highlight_end":20}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rustyline-12.0.0/src/lib.rs","byte_start":19457,"byte_end":19458,"line_start":587,"line_end":587,"column_start":30,"column_end":31,"is_primary":false,"text":[{"text":"pub struct Editor<H: Helper, I: History> {","highlight_start":30,"highlight_end":31}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rustyline-12.0.0/src/lib.rs","byte_start":19439,"byte_end":19445,"line_start":587,"line_end":587,"column_start":12,"column_end":18,"is_primary":true,"text":[{"text":"pub struct Editor<H: Helper, I: History> {","highlight_start":12,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"add missing generic argument","code":null,"level":"help","spans":[{"file_name":"src/cli/input.rs","byte_start":125,"byte_end":125,"line_start":5,"line_end":5,"column_start":22,"column_end":22,"is_primary":true,"text":[{"text":"    editor: Editor<()>,","highlight_start":22,"highlight_end":22}],"label":null,"suggested_replacement":", I","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0107]\u001b[0m\u001b[0m\u001b[1m: struct takes 2 generic arguments but 1 generic argument was supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli/input.rs:5:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    editor: Editor<()>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12msupplied 1 generic argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected 2 generic arguments\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: struct defined here, with 2 generic parameters: `H`, `I`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rustyline-12.0.0/src/lib.rs:587:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m587\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct Editor<H: Helper, I: History> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: add missing generic argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m    editor: Editor<()\u001b[0m\u001b[0m\u001b[38;5;10m, I\u001b[0m\u001b[0m>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[38;5;10m+++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error; 1 warning emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 1 previous error; 1 warning emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0107`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about this error, try `rustc --explain E0107`.\u001b[0m\n"}
