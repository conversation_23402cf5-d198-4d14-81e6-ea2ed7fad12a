# Minimal Agent CLI 使用指南

## 概述

Minimal Agent 是一个基于 LLM 驱动的智能代码助手，专注于命令行交互体验。它提供了丰富的工具集合，可以帮助您进行代码编写、文件管理、项目分析等任务。

## 功能特性

### 🎯 核心功能
- **智能对话**: 基于 Claude-3.5 Sonnet 的自然语言交互
- **工具调用**: 丰富的内置工具集合
- **代码助手**: 专门优化的代码编写和分析功能
- **文件管理**: 完整的文件操作支持
- **项目分析**: 代码结构分析和搜索功能

### 🔧 内置工具
- **Echo Tool**: 回显测试工具
- **Calculator Tool**: 数学计算工具
- **File Read Tool**: 文件读取工具
- **File Write Tool**: 文件写入工具
- **File Delete Tool**: 文件删除工具
- **List Directory Tool**: 目录列表工具
- **File Search Tool**: 文件搜索工具
- **Code Analysis Tool**: 代码分析工具
- **Project Structure Tool**: 项目结构分析工具
- **Editor Tool**: 代码编辑工具

## 安装和配置

### 环境要求
- Rust 1.70+
- 有效的 API 密钥 (OpenRouter 或 OpenAI)

### 设置 API 密钥

#### OpenRouter (推荐)
```bash
export OPENROUTER_API_KEY="your-openrouter-api-key"
```

#### OpenAI
```bash
export OPENAI_API_KEY="your-openai-api-key"
```

### 编译项目
```bash
cargo build --release
```

## 使用方法

### 快速启动

#### 使用启动脚本 (推荐)
```bash
# 交互模式
./start_cli.sh interactive

# 单次查询模式
./start_cli.sh single

# 指定提供商和模型
./start_cli.sh interactive openrouter anthropic/claude-3.5-sonnet
```

#### 直接使用 Cargo
```bash
# 交互模式
cargo run -- --interactive --coding-mode

# 单次查询模式
cargo run -- --coding-mode

# 测试模式 (使用更便宜的模型)
cargo run -- --interactive --test-mode --coding-mode
```

### 命令行参数

| 参数 | 描述 | 默认值 |
|------|------|--------|
| `--interactive` / `-i` | 启用交互模式 | false |
| `--provider` | LLM 提供商 (openai, openrouter) | openrouter |
| `--model` | 使用的模型 | anthropic/claude-3.5-sonnet |
| `--api-key` | API 密钥 | 从环境变量读取 |
| `--test-mode` | 使用测试模式 (更便宜的模型) | false |
| `--coding-mode` | 启用代码助手模式 | false |

## 交互模式

### 内置命令

在交互模式下，您可以使用以下内置命令：

| 命令 | 描述 |
|------|------|
| `help` | 显示帮助信息 |
| `status` | 显示 Agent 状态 |
| `history` | 显示对话历史 |
| `clear` | 清屏 |
| `quit` / `exit` | 退出程序 |

### 示例对话

```
🤖 Minimal Agent - 智能代码助手
✨ 基于 LLM 驱动的工具调用架构
────────────────────────────────────────────────────────────

交互模式已启动
输入 help 查看可用命令，输入 quit 退出

🔧 可用工具:
  ├─ echo
  ├─ calculator
  ├─ file_read
  ├─ file_write
  ├─ file_delete
  ├─ list_directory
  ├─ file_search
  ├─ code_analysis
  ├─ project_structure
  └─ editor

👤 echo hello world
🤖 hello world

👤 calculate 15 + 25 * 2
🤖 65

👤 read the file src/main.rs
🤖 [文件内容显示...]

👤 create a new Python file with a hello world function
🤖 [创建文件并显示内容...]
```

## 使用场景

### 代码编写助手
```bash
# 启动代码助手模式
cargo run -- --interactive --coding-mode

# 示例对话
👤 create a new Rust function that calculates fibonacci numbers
👤 analyze the code structure of this project
👤 search for all TODO comments in the codebase
👤 refactor the main.rs file to improve readability
```

### 文件管理
```bash
👤 list all files in the src directory
👤 read the content of Cargo.toml
👤 create a new configuration file with default settings
👤 delete the temporary files in the target directory
```

### 项目分析
```bash
👤 analyze the project structure and dependencies
👤 find all functions that contain error handling
👤 show me the most complex functions in the codebase
👤 generate documentation for the main modules
```

## 高级功能

### 自定义提示词
项目支持不同的系统提示词模式：
- `general`: 通用助手模式
- `coding`: 代码助手模式 (推荐用于开发)

### 模型选择
支持多种 LLM 模型：
- `anthropic/claude-3.5-sonnet` (推荐)
- `anthropic/claude-3-haiku` (测试模式)
- `gpt-4` (OpenAI)
- `gpt-3.5-turbo` (OpenAI 测试模式)

### 日志配置
```bash
# 启用详细日志
RUST_LOG=debug cargo run -- --interactive

# 只显示 Agent 相关日志
RUST_LOG=minimal_agent=info cargo run -- --interactive
```

## 故障排除

### 常见问题

#### API 密钥错误
```
Error: API key not provided
```
**解决方案**: 确保设置了正确的环境变量或使用 `--api-key` 参数。

#### 网络连接问题
```
Error: Failed to connect to API
```
**解决方案**: 检查网络连接和 API 服务状态。

#### 模型不可用
```
Error: Model not found
```
**解决方案**: 检查模型名称是否正确，或切换到其他可用模型。

### 调试模式
```bash
# 启用详细错误信息
RUST_BACKTRACE=1 cargo run -- --interactive --coding-mode

# 使用测试模式进行调试
cargo run -- --interactive --test-mode --coding-mode
```

## 开发和扩展

### 添加新工具
1. 在 `src/tools/builtin/` 目录下创建新工具
2. 实现 `Tool` trait
3. 在 `src/main.rs` 中注册工具

### 自定义 CLI 界面
CLI 界面相关代码位于 `src/cli/` 目录：
- `display.rs`: 显示和格式化
- `commands.rs`: 命令处理
- `ui.rs`: 用户界面管理

## 性能优化

### 模型选择建议
- **开发测试**: 使用 `--test-mode` 和 Claude-3 Haiku
- **生产使用**: 使用 Claude-3.5 Sonnet
- **成本敏感**: 使用 GPT-3.5 Turbo

### 资源使用
- 内存使用: ~50MB (基础运行)
- 网络使用: 取决于对话长度和频率
- 磁盘使用: 对话历史存储在 SQLite 数据库中

## 许可证

MIT License - 详见 LICENSE 文件。
