# Minimal Agent

一个基于 LLM 驱动的智能代码助手，专注于命令行交互体验。

## 功能特性

- 🤖 **LLM 驱动**: 支持 OpenAI 和 OpenRouter API
- 🔧 **工具调用**: 可扩展的工具系统
- 💬 **对话管理**: 持久化对话历史
- 🎯 **代码助手**: 专门的代码编写和分析工具
- 🖥️ **优化 CLI**: 美观的命令行交互界面
- ⚡ **高性能**: 轻量级设计，快速响应

## 内置工具

- **Echo Tool**: 回显测试工具
- **Calculator Tool**: 数学计算工具
- **File Read Tool**: 文件读取工具
- **File Write Tool**: 文件写入工具
- **File Delete Tool**: 文件删除工具
- **List Directory Tool**: 目录列表工具
- **File Search Tool**: 文件搜索工具
- **Code Analysis Tool**: 代码分析工具
- **Project Structure Tool**: 项目结构分析工具
- **Editor Tool**: 代码编辑工具

## CLI 界面特性

### 🎨 美观的界面
- 彩色输出和图标
- 清晰的状态指示
- 进度条显示
- 分隔线和格式化

### 📋 内置命令
- `help` - 显示帮助信息
- `status` - 显示 Agent 状态
- `history` - 显示对话历史
- `clear` - 清屏
- `quit/exit` - 退出程序

### ⚡ 交互体验
- 实时处理状态
- 错误高亮显示
- 智能命令补全
- 历史记录管理

## 快速开始

### 环境要求

- Rust 1.70+
- SQLite 3
- 有效的 API 密钥 (OpenRouter 或 OpenAI)

### 安装

1. 克隆仓库:
```bash
git clone <repository-url>
cd minimal_agent
```

2. 设置环境变量:
```bash
# 使用 OpenRouter (推荐)
export OPENROUTER_API_KEY="your-openrouter-api-key"

# 或使用 OpenAI
export OPENAI_API_KEY="your-openai-api-key"
```

3. 编译项目:
```bash
cargo build --release
```

### 使用方法

#### 快速启动 (推荐)

```bash
# 交互模式
./start_cli.sh interactive

# 单次查询模式
./start_cli.sh single

# 指定提供商和模型
./start_cli.sh interactive openrouter anthropic/claude-3.5-sonnet
```

#### 直接使用 Cargo

```bash
# 交互模式
cargo run -- --interactive --coding-mode

# 单次查询
cargo run -- --coding-mode

# 使用测试模式 (更便宜的模型)
cargo run -- --interactive --test-mode --coding-mode
```

## 配置

### 支持的 LLM 提供商

#### OpenRouter (推荐)
- 支持多种模型
- 更好的价格和可用性
- 设置 `OPENROUTER_API_KEY` 环境变量

#### OpenAI
- 官方 OpenAI API
- 设置 `OPENAI_API_KEY` 环境变量

### 模型选择

```bash
# 使用 Claude-3.5 Sonnet (默认)
cargo run -- --model "anthropic/claude-3.5-sonnet"

# 使用 GPT-4
cargo run -- --provider openai --model "gpt-4"

# 测试模式 (使用更便宜的模型)
cargo run -- --test-mode
```

## 示例用法

### CLI 交互示例

```
🤖 Minimal Agent - 智能代码助手
✨ 基于 LLM 驱动的工具调用架构
────────────────────────────────────────────────────────────

交互模式已启动
输入 help 查看可用命令，输入 quit 退出

🔧 可用工具:
  ├─ echo
  ├─ calculator
  ├─ file_read
  ├─ file_write
  ├─ file_delete
  ├─ list_directory
  ├─ file_search
  ├─ code_analysis
  ├─ project_structure
  └─ editor

👤 echo hello world
⏳ 正在处理您的请求...
🤖 hello world

👤 calculate 15 + 25 * 2
⏳ 正在处理您的请求...
🤖 65

👤 read the file src/main.rs
⏳ 正在处理您的请求...
🤖 [文件内容显示...]
```

### 代码助手示例

```bash
# 启用代码助手模式
cargo run -- --interactive --coding-mode
```

```
👤 analyze the code structure of this project
⏳ 正在处理您的请求...
🤖 [项目结构分析...]

👤 create a new Rust function that calculates fibonacci numbers
⏳ 正在处理您的请求...
🤖 [创建 Fibonacci 函数...]

👤 search for all TODO comments in the codebase
⏳ 正在处理您的请求...
🤖 [搜索结果...]
```

## 开发

### 添加新工具

1. 在 `src/tools/builtin/` 目录下创建新工具
2. 实现 `Tool` trait
3. 在 `src/main.rs` 中注册工具

### 项目结构

```
src/
├── agent/          # Agent 核心逻辑
├── cli/           # CLI 界面和交互
├── database/       # 数据库操作
├── llm/           # LLM 客户端
├── tools/         # 工具系统
├── prompts/       # 系统提示词
└── main.rs        # CLI 入口点
```

## 文档

- [CLI 使用指南](CLI_GUIDE.md) - 详细的 CLI 使用说明
- [代码助手指南](CODING_ASSISTANT_GUIDE.md) - 代码助手功能说明
- [OpenRouter 指南](OPENROUTER_GUIDE.md) - OpenRouter API 配置

## 许可证

MIT License
