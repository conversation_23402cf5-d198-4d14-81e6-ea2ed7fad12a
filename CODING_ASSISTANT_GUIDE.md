# 代码编写助手使用指南

Minimal Agent 现在配备了强大的文件编辑和代码分析工具，可以作为您的智能编程助手。

## 🚀 快速开始

### 命令行模式

```bash
# 启用代码编写模式
cargo run -- --coding-mode --interactive

# 或使用测试模式（更便宜的模型）
cargo run -- --coding-mode --test-mode --interactive
```

### Web 界面模式

```bash
# 启动 Web 服务器（代码编写模式）
cargo run --bin web_server -- --coding-mode

# 然后访问 http://localhost:3000
```

### 演示模式

```bash
# 运行代码编写助手演示
cargo run --example coding_assistant
```

## 🔧 可用工具

### 文件操作工具

1. **read_file** - 读取文件内容
   ```
   "Read the contents of src/main.rs"
   "Show me the package.json file"
   ```

2. **write_file** - 创建或修改文件
   ```
   "Create a new Python file called hello.py with a simple function"
   "Update the README.md file with installation instructions"
   ```

3. **delete_file** - 删除文件或目录
   ```
   "Delete the temporary file temp.txt"
   "Remove the old_code directory"
   ```

4. **list_directory** - 列出目录内容
   ```
   "List all files in the src directory"
   "Show me the project structure"
   ```

5. **search_files** - 搜索文件
   ```
   "Find all Python files in this project"
   "Search for files containing the word 'function'"
   ```

### 代码分析工具

1. **analyze_code** - 分析代码结构
   ```
   "Analyze the code structure of src/main.rs"
   "What functions are defined in this Python file?"
   ```

2. **analyze_project_structure** - 分析项目结构
   ```
   "Analyze the overall structure of this project"
   "What type of project is this and what languages does it use?"
   ```

## 💡 使用示例

### 1. 创建新的代码文件

```
👤 You: Create a new Python file called calculator.py with functions for basic arithmetic operations

🤖 Agent: I'll create a Python calculator file with basic arithmetic functions for you.

[使用 write_file 工具创建文件]

✅ Created calculator.py with the following functions:
- add(a, b): Addition
- subtract(a, b): Subtraction  
- multiply(a, b): Multiplication
- divide(a, b): Division with error handling
```

### 2. 分析现有代码

```
👤 You: Analyze the code structure of this Rust project

🤖 Agent: I'll analyze the structure of this Rust project for you.

[使用 analyze_project_structure 和 list_directory 工具]

📊 Project Analysis:
- Project Type: Rust (Cargo.toml detected)
- Main Components: Agent system with LLM integration
- Key Directories: src/, examples/, static/
- Languages: Rust (primary), HTML/CSS/JS (web interface)
```

### 3. 修改现有文件

```
👤 You: Read the main.rs file and add better error handling to the main function

🤖 Agent: I'll examine the current main.rs file and improve its error handling.

[使用 read_file 分析现有代码，然后使用 write_file 更新]

✅ Updated main.rs with:
- Better error propagation using Result<>
- More descriptive error messages
- Graceful handling of API key errors
```

### 4. 搜索和重构

```
👤 You: Find all files that use the old API and update them to use the new interface

🤖 Agent: I'll search for files using the old API and update them.

[使用 search_files 查找相关文件，然后逐个使用 read_file 和 write_file 更新]

🔍 Found 3 files using the old API:
- src/client.rs: Updated API calls
- examples/demo.rs: Migrated to new interface  
- tests/integration.rs: Updated test cases
```

## 🎯 最佳实践

### 代码编写工作流

1. **项目分析**
   ```
   "Analyze the structure of this project"
   "What coding standards does this project follow?"
   ```

2. **需求理解**
   ```
   "Read the existing code to understand the current implementation"
   "What patterns are used in similar files?"
   ```

3. **增量开发**
   ```
   "Create a new module following the existing project structure"
   "Add unit tests for the new functionality"
   ```

4. **代码审查**
   ```
   "Review the code I just wrote and suggest improvements"
   "Check if the new code follows the project's conventions"
   ```

### 安全考虑

- ✅ **允许的操作**: 在当前项目目录下的文件操作
- ❌ **禁止的操作**: 访问系统文件、敏感目录、上级目录
- 🔒 **自动保护**: 防止路径遍历攻击和敏感文件访问

### 文件管理建议

1. **备份重要文件**: 在大幅修改前先备份
2. **增量修改**: 逐步修改而不是完全重写
3. **测试验证**: 修改后验证代码是否正常工作
4. **版本控制**: 使用 Git 等工具跟踪变更

## 🌟 高级功能

### 多文件项目开发

```
👤 You: Create a complete REST API project in Python with the following structure:
- main.py (FastAPI application)
- models.py (data models)
- routes.py (API endpoints)  
- requirements.txt (dependencies)

🤖 Agent: I'll create a complete REST API project structure for you.

[依次创建多个文件，确保它们之间的依赖关系正确]
```

### 代码重构

```
👤 You: Refactor the large main.rs file by splitting it into separate modules

🤖 Agent: I'll help you refactor main.rs into a more modular structure.

[分析现有代码，创建新的模块文件，更新导入语句]
```

### 文档生成

```
👤 You: Generate comprehensive documentation for all the public functions in this project

🤖 Agent: I'll analyze all the code files and generate documentation.

[扫描所有源文件，提取公共 API，生成文档]
```

## 🛠️ 故障排除

### 常见问题

1. **文件权限错误**
   ```
   Error: Permission denied
   解决方案: 确保对目标目录有写权限
   ```

2. **路径不存在**
   ```
   Error: No such file or directory
   解决方案: 使用 list_directory 确认路径是否正确
   ```

3. **文件已存在**
   ```
   Warning: File already exists
   解决方案: 先读取现有文件，然后决定是否覆盖
   ```

### 调试技巧

1. **验证文件操作**
   ```
   "List the directory to confirm the file was created"
   "Read the file back to verify the content"
   ```

2. **检查代码语法**
   ```
   "Analyze the code I just wrote for syntax errors"
   "Validate the Python syntax in the new file"
   ```

## 📚 示例项目

### Python Web 应用

```bash
# 让 Agent 创建一个完整的 Flask 应用
"Create a Flask web application with user authentication, including:
- app.py (main application)
- models.py (user model)
- templates/ (HTML templates)
- static/ (CSS/JS files)
- requirements.txt"
```

### Rust CLI 工具

```bash
# 创建一个 Rust 命令行工具
"Create a Rust CLI tool that processes CSV files, including:
- main.rs (CLI interface)
- lib.rs (core logic)
- Cargo.toml (dependencies)
- README.md (usage instructions)"
```

### JavaScript 项目

```bash
# 创建一个 Node.js 项目
"Set up a Node.js project with Express.js, including:
- package.json
- server.js (Express server)
- routes/ (API routes)
- public/ (static files)
- .gitignore"
```

## 🎓 学习资源

- **代码模式**: Agent 会学习并遵循项目中的现有代码模式
- **最佳实践**: 自动应用语言特定的最佳实践
- **错误处理**: 提供适当的错误处理和边界情况考虑
- **文档**: 自动生成注释和文档

---

**开始使用**: `cargo run -- --coding-mode --interactive`

**Web 界面**: `cargo run --bin web_server -- --coding-mode`
