#!/bin/bash

# Minimal Agent Web 服务器启动脚本

echo "🤖 Minimal Agent Web 服务器启动脚本"
echo "=================================="

# 检查是否设置了 API 密钥
if [ -z "$OPENROUTER_API_KEY" ] && [ -z "$OPENAI_API_KEY" ]; then
    echo "⚠️  警告: 未检测到 API 密钥"
    echo "请设置以下环境变量之一:"
    echo "  export OPENROUTER_API_KEY='your-openrouter-key'"
    echo "  export OPENAI_API_KEY='your-openai-key'"
    echo ""
    echo "或者使用 --api-key 参数"
    echo ""
fi

# 检查端口参数
PORT=${1:-3000}

echo "🚀 启动 Web 服务器..."
echo "📍 端口: $PORT"
echo "🗄️  数据库: conversations.db"
echo "🧪 使用测试模式 (Claude-3 Haiku)"
echo ""

# 启动服务器
cargo run --bin web_server -- --port "$PORT" --test-mode

echo ""
echo "👋 服务器已停止"
