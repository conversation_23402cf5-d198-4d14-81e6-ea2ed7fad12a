#!/bin/bash

# Minimal Agent CLI 演示脚本

echo "🎬 Minimal Agent CLI 功能演示"
echo "================================"
echo ""

# 检查是否有 API 密钥
if [ -z "$OPENROUTER_API_KEY" ] && [ -z "$OPENAI_API_KEY" ]; then
    echo "⚠️  注意: 未设置 API 密钥，将使用测试密钥进行演示"
    export OPENROUTER_API_KEY="demo-key"
fi

echo "📋 演示内容:"
echo "1. 显示帮助信息"
echo "2. 展示美观的 CLI 界面"
echo "3. 演示工具列表"
echo "4. 模拟交互过程"
echo ""

echo "🚀 启动 CLI 界面..."
echo ""

# 创建演示输入文件
cat > demo_input.txt << 'EOF'
help
status
quit
EOF

echo "📝 模拟用户输入:"
echo "  > help"
echo "  > status" 
echo "  > quit"
echo ""

echo "🎯 执行演示..."
echo ""

# 运行演示
cargo run --release -- --interactive --test-mode --coding-mode --api-key "demo-key" < demo_input.txt

echo ""
echo "✅ 演示完成！"
echo ""
echo "💡 要体验完整功能，请:"
echo "1. 设置真实的 API 密钥"
echo "2. 运行: ./start_cli.sh interactive"
echo "3. 或者: cargo run -- --interactive --coding-mode"
echo ""

# 清理临时文件
rm -f demo_input.txt

echo "📚 更多信息请查看:"
echo "  - CLI_GUIDE.md - 详细使用指南"
echo "  - README.md - 项目概述"
echo "  - CODING_ASSISTANT_GUIDE.md - 代码助手功能"
