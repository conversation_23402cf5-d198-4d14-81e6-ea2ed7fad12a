use minimal_agent::{
    Agent, AgentConfig, LlmConfig,
    tools::{ToolRegistry, builtin::{EchoTool, CalculatorTool}},
};
use std::sync::Arc;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt()
        .with_env_filter("minimal_agent=info")
        .init();

    // 创建 Agent 配置
    let config = AgentConfig {
        llm: LlmConfig {
            provider: "openrouter".to_string(),
            model: "anthropic/claude-3.5-sonnet".to_string(),
            api_key: std::env::var("OPENROUTER_API_KEY")
                .or_else(|_| std::env::var("OPENAI_API_KEY"))
                .unwrap_or_else(|_| "your-api-key-here".to_string()),
            base_url: None,
            temperature: Some(0.7),
            max_tokens: Some(4096),
        },
        max_iterations: 5,
        max_tool_calls_per_iteration: 3,
        system_prompt: Some(
            "You are a helpful assistant with access to tools. \
             Use them to help answer questions.".to_string()
        ),
    };

    // 创建工具注册表
    let mut tool_registry = ToolRegistry::new();
    tool_registry.register_tool(Arc::new(EchoTool))?;
    tool_registry.register_tool(Arc::new(CalculatorTool))?;

    println!("🤖 Minimal Agent - Basic Usage Example");
    println!("Available tools: {:?}", tool_registry.get_tool_names());

    // 创建 Agent
    let mut agent = Agent::new(config, tool_registry);

    // 开始对话
    agent.start_conversation();

    // 示例查询
    let queries = vec![
        "Hello! Can you echo 'Hello World' for me?",
        "What is 15 + 25?",
        "Calculate 10 * 5 and then add 3 to the result",
    ];

    for (i, query) in queries.iter().enumerate() {
        println!("\n--- Query {} ---", i + 1);
        println!("👤 User: {}", query);
        
        match agent.process_user_input(query.to_string()).await {
            Ok(response) => {
                println!("🤖 Agent: {}", response);
            }
            Err(e) => {
                println!("❌ Error: {}", e);
            }
        }
    }

    println!("\n✅ Example completed!");
    Ok(())
}
