use reqwest::Client;
use serde_json::{json, Value};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🔍 OpenRouter 模型检查工具");
    println!("========================");

    let api_key = std::env::var("OPENROUTER_API_KEY")
        .unwrap_or_else(|_| {
            eprintln!("❌ 错误: 未找到 OPENROUTER_API_KEY 环境变量");
            std::process::exit(1);
        });

    let client = Client::new();

    // 获取可用模型列表
    println!("📋 获取 OpenRouter 可用模型...");
    
    let response = client
        .get("https://openrouter.ai/api/v1/models")
        .header("Authorization", format!("Bearer {}", api_key))
        .send()
        .await?;

    if !response.status().is_success() {
        eprintln!("❌ 获取模型列表失败: {}", response.status());
        let error_text = response.text().await?;
        eprintln!("错误详情: {}", error_text);
        return Ok(());
    }

    let models: Value = response.json().await?;
    
    if let Some(data) = models.get("data").and_then(|d| d.as_array()) {
        println!("✅ 找到 {} 个可用模型", data.len());
        println!();

        // 筛选 Anthropic Claude 模型
        println!("🤖 Anthropic Claude 模型:");
        println!("{:<40} {:<15} {:<10}", "模型 ID", "上下文长度", "价格/1M tokens");
        println!("{}", "-".repeat(70));

        let mut claude_models = Vec::new();
        
        for model in data {
            if let Some(id) = model.get("id").and_then(|i| i.as_str()) {
                if id.contains("anthropic/claude") {
                    let context_length = model
                        .get("context_length")
                        .and_then(|c| c.as_u64())
                        .unwrap_or(0);
                    
                    let pricing = model
                        .get("pricing")
                        .and_then(|p| p.get("prompt"))
                        .and_then(|p| p.as_str())
                        .unwrap_or("N/A");

                    println!("{:<40} {:<15} ${:<10}", id, format!("{}K", context_length / 1000), pricing);
                    claude_models.push(id);
                }
            }
        }

        println!();
        println!("🧪 推荐的测试模型 (便宜):");
        for model in &claude_models {
            if model.contains("haiku") {
                println!("  • {}", model);
            }
        }

        println!();
        println!("⚡ 推荐的生产模型 (平衡):");
        for model in &claude_models {
            if model.contains("sonnet") {
                println!("  • {}", model);
            }
        }

        println!();
        println!("🚀 推荐的高级模型 (强大):");
        for model in &claude_models {
            if model.contains("opus") {
                println!("  • {}", model);
            }
        }

        // 测试一个简单的 API 调用
        println!();
        println!("🧪 测试 API 调用...");
        
        let test_model = claude_models
            .iter()
            .find(|m| m.contains("haiku"))
            .or_else(|| claude_models.first())
            .unwrap_or(&"anthropic/claude-3-haiku");

        println!("使用模型: {}", test_model);

        let test_payload = json!({
            "model": test_model,
            "messages": [
                {
                    "role": "user",
                    "content": "Hello! Please respond with just 'API test successful'."
                }
            ],
            "max_tokens": 50
        });

        let test_response = client
            .post("https://openrouter.ai/api/v1/chat/completions")
            .header("Authorization", format!("Bearer {}", api_key))
            .header("Content-Type", "application/json")
            .header("HTTP-Referer", "https://github.com/your-username/minimal_agent")
            .header("X-Title", "Minimal Agent Model Checker")
            .json(&test_payload)
            .send()
            .await?;

        if test_response.status().is_success() {
            let result: Value = test_response.json().await?;
            if let Some(content) = result
                .get("choices")
                .and_then(|c| c.get(0))
                .and_then(|c| c.get("message"))
                .and_then(|m| m.get("content"))
                .and_then(|c| c.as_str())
            {
                println!("✅ API 测试成功!");
                println!("📝 响应: {}", content.trim());
            }
        } else {
            println!("❌ API 测试失败: {}", test_response.status());
            let error_text = test_response.text().await?;
            println!("错误详情: {}", error_text);
        }

    } else {
        println!("❌ 无法解析模型列表");
    }

    println!();
    println!("💡 使用建议:");
    println!("  • 开发/测试: 使用 claude-3-haiku (最便宜)");
    println!("  • 日常使用: 使用 claude-3.5-sonnet (平衡)");
    println!("  • 复杂任务: 使用 claude-3-opus (最强大)");

    Ok(())
}
