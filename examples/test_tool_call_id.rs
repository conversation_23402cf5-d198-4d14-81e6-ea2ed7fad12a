use minimal_agent::{
    Agent, AgentConfig, LlmConfig,
    tools::ToolRegistry,
    types::{Message, MessageRole},
};
use std::sync::Arc;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt()
        .with_env_filter("minimal_agent=debug")
        .init();

    println!("🔧 Tool Call ID 测试");
    println!("==================");

    // 检查环境变量
    let api_key = std::env::var("OPENROUTER_API_KEY")
        .or_else(|_| std::env::var("OPENAI_API_KEY"))
        .unwrap_or_else(|_| {
            eprintln!("❌ 错误: 未找到 API 密钥");
            eprintln!("请设置 OPENROUTER_API_KEY 或 OPENAI_API_KEY 环境变量");
            std::process::exit(1);
        });

    println!("✅ 找到 API 密钥");

    // 创建 LLM 配置
    let llm_config = LlmConfig {
        provider: "openrouter".to_string(),
        model: "anthropic/claude-3.5-sonnet".to_string(),
        api_key,
        base_url: None,
        temperature: Some(0.7),
        max_tokens: Some(1000),
    };

    // 创建 Agent 配置
    let config = AgentConfig {
        llm: llm_config,
        max_iterations: 3,
        max_tool_calls_per_iteration: 2,
        system_prompt: Some("You are a helpful assistant. Use the echo tool to test tool calling.".to_string()),
    };

    // 创建工具注册表
    let mut tool_registry = ToolRegistry::new();
    
    // 注册内置工具
    minimal_agent::tools::builtin::register_builtin_tools(&mut tool_registry)?;

    println!("✅ 注册了 {} 个工具", tool_registry.list_tools().len());

    // 创建 Agent
    let mut agent = Agent::new(config, Arc::new(tool_registry))?;

    println!("🤖 Agent 创建成功");

    // 测试工具调用
    println!("\n📤 发送消息: 'Please use the echo tool to say hello'");
    
    match agent.process_user_input("Please use the echo tool to say hello".to_string()).await {
        Ok(response) => {
            println!("✅ Agent 响应: {}", response);
            
            // 检查对话历史中的工具调用ID
            if let Some(history) = agent.get_conversation_history() {
                println!("\n📋 对话历史:");
                for (i, message) in history.iter().enumerate() {
                    println!("  {}. {} 消息: {}", i + 1, format!("{:?}", message.role), message.content);
                    
                    if let Some(tool_calls) = &message.tool_calls {
                        println!("     🔧 工具调用:");
                        for call in tool_calls {
                            println!("       - ID: {}, 名称: {}", call.id, call.name);
                        }
                    }
                    
                    if let Some(tool_call_id) = &message.tool_call_id {
                        println!("     🔗 工具调用ID: {}", tool_call_id);
                    }
                }
            }
        }
        Err(e) => {
            eprintln!("❌ Agent 处理失败: {}", e);
            std::process::exit(1);
        }
    }

    println!("\n✅ 测试完成");
    Ok(())
}
