use minimal_agent::{
    Agent, AgentConfig, LlmConfig,
    tools::{ToolRegistry, builtin::{
        EchoTool, CalculatorTool, FileReadTool, FileWriteTool, FileDeleteTool,
        ListDirectoryTool, FileSearchTool, CodeAnalysisTool, ProjectStructureTool
    }},
    prompts,
};
use std::sync::Arc;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt()
        .with_env_filter("minimal_agent=info")
        .init();

    println!("🤖 Minimal Agent - Coding Assistant Demo");
    println!("========================================");

    // 检查 API 密钥
    let api_key = std::env::var("OPENROUTER_API_KEY")
        .or_else(|_| std::env::var("OPENAI_API_KEY"))
        .unwrap_or_else(|_| {
            eprintln!("Warning: No API key found. Set OPENROUTER_API_KEY or OPENAI_API_KEY");
            "your-api-key-here".to_string()
        });

    // 创建代码编写专用配置
    let config = AgentConfig {
        llm: LlmConfig {
            provider: "openrouter".to_string(),
            model: "anthropic/claude-3.5-sonnet".to_string(),
            api_key,
            base_url: None,
            temperature: Some(0.3), // 较低的温度以获得更一致的代码
            max_tokens: Some(4096),
        },
        max_iterations: 15, // 更多迭代以支持复杂的代码任务
        max_tool_calls_per_iteration: 8,
        system_prompt: Some(prompts::get_system_prompt("coding").to_string()),
    };

    // 创建工具注册表并注册所有代码编写工具
    let mut tool_registry = ToolRegistry::new();
    
    // 基础工具
    tool_registry.register_tool(Arc::new(EchoTool))?;
    tool_registry.register_tool(Arc::new(CalculatorTool))?;
    
    // 文件操作工具
    tool_registry.register_tool(Arc::new(FileReadTool))?;
    tool_registry.register_tool(Arc::new(FileWriteTool))?;
    tool_registry.register_tool(Arc::new(FileDeleteTool))?;
    tool_registry.register_tool(Arc::new(ListDirectoryTool))?;
    tool_registry.register_tool(Arc::new(FileSearchTool))?;
    
    // 代码分析工具
    tool_registry.register_tool(Arc::new(CodeAnalysisTool))?;
    tool_registry.register_tool(Arc::new(ProjectStructureTool))?;

    println!("✅ Registered {} coding tools", tool_registry.tool_count());

    // 创建 Agent
    let mut agent = Agent::new(config, tool_registry);

    // 演示代码编写任务
    let coding_tasks = vec![
        "Analyze the structure of this Rust project and tell me what it does",
        "Create a simple Python script that calculates the factorial of a number",
        "Read the main.rs file and explain its key components",
        "Create a JavaScript function that validates email addresses",
        "Search for all Rust files in this project and list their purposes",
    ];

    println!("\n🚀 Running coding assistant demonstrations...\n");

    for (i, task) in coding_tasks.iter().enumerate() {
        println!("📝 Task {}: {}", i + 1, task);
        println!("{}", "=".repeat(60));

        // 开始新对话
        agent.start_conversation();

        match agent.process_user_input(task.to_string()).await {
            Ok(response) => {
                println!("🤖 Response:\n{}\n", response);
            }
            Err(e) => {
                println!("❌ Error: {}\n", e);
            }
        }

        println!("{}\n", "-".repeat(60));
    }

    println!("✅ Coding assistant demonstration completed!");
    println!("\n💡 To use the coding assistant interactively:");
    println!("   cargo run -- --coding-mode --interactive");
    println!("\n🌐 To use the web interface:");
    println!("   cargo run --bin web_server -- --coding-mode");

    Ok(())
}
