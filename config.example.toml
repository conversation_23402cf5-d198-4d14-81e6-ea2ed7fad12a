# Minimal Agent 配置文件示例
# 复制此文件为 config.toml 并修改相应的配置

[llm]
# LLM 提供商 (支持 "openrouter", "openai")
provider = "openrouter"

# 模型名称
# OpenRouter 上的 Claude-3.5 Sonnet (推荐)
model = "anthropic/claude-3.5-sonnet"
# 其他可用模型:
# model = "anthropic/claude-3-opus"      # 最强大
# model = "anthropic/claude-3-haiku"    # 最便宜
# model = "openai/gpt-4-turbo"
# model = "openai/gpt-3.5-turbo"

# API 密钥 (也可以通过环境变量设置)
# OpenRouter: OPENROUTER_API_KEY
# OpenAI: OPENAI_API_KEY
api_key = "your-openrouter-api-key-here"

# 可选：自定义 API 基础 URL
# base_url = "https://openrouter.ai/api/v1"

# 可选：温度参数 (0.0 - 2.0)
temperature = 0.7

# 可选：最大 token 数
max_tokens = 4096

[agent]
# 最大推理循环次数
max_iterations = 10

# 每次迭代最大工具调用数
max_tool_calls_per_iteration = 5

# 系统提示词
system_prompt = """You are a helpful AI assistant with access to tools.
Use the available tools to help answer questions and solve problems.
Always explain what you're doing when using tools.
Be concise but thorough in your responses."""
