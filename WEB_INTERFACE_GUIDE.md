# Web 界面使用指南

## 概述

Minimal Agent 现在提供了一个现代化的 Web 界面，支持：
- 📱 响应式设计，支持桌面和移动设备
- 💬 实时对话界面
- 📚 SQLite 数据库存储对话历史
- 🔧 工具调用可视化
- 🗂️ 多对话管理

## 快速开始

### 1. 启动 Web 服务器

```bash
# 使用默认设置启动
cargo run --bin web_server

# 或指定端口和数据库
cargo run --bin web_server -- --port 8080 --database sqlite:my_conversations.db
```

### 2. 访问 Web 界面

打开浏览器访问：http://localhost:3000

## 命令行选项

```bash
cargo run --bin web_server -- --help
```

### 主要选项

- `--port, -p`: 指定端口 (默认: 3000)
- `--database, -d`: SQLite 数据库路径 (默认: sqlite:conversations.db)
- `--api-key`: API 密钥
- `--provider`: LLM 提供商 (openrouter, openai)
- `--model`: 模型名称
- `--test-mode`: 使用便宜模型进行测试

### 示例命令

```bash
# 基本启动
cargo run --bin web_server

# 指定端口和测试模式
cargo run --bin web_server -- --port 8080 --test-mode

# 使用 OpenAI
cargo run --bin web_server -- --provider openai --model gpt-4-turbo-preview

# 自定义数据库位置
cargo run --bin web_server -- --database sqlite:/path/to/conversations.db
```

## 功能特性

### 对话管理

1. **创建新对话**: 点击侧边栏的"新对话"按钮
2. **选择对话**: 点击侧边栏中的任意对话
3. **删除对话**: 选择对话后点击删除按钮
4. **自动保存**: 所有消息自动保存到数据库

### 消息功能

1. **发送消息**: 在输入框中输入消息，按 Enter 或点击发送按钮
2. **多行输入**: 使用 Shift+Enter 换行
3. **工具调用**: Agent 使用工具时会显示详细信息
4. **历史记录**: 所有对话历史永久保存

### 界面特性

1. **响应式设计**: 自动适配不同屏幕尺寸
2. **实时更新**: 消息实时显示，无需刷新
3. **加载指示**: 处理消息时显示加载动画
4. **错误处理**: 友好的错误提示

## API 端点

Web 服务器提供以下 REST API：

### 对话管理

```http
GET /api/conversations
# 获取所有对话列表

POST /api/conversations
# 创建新对话
Content-Type: application/json
{
  "title": "可选的对话标题"
}

DELETE /api/conversations/{id}
# 删除指定对话
```

### 消息管理

```http
GET /api/conversations/{id}/messages
# 获取对话的所有消息

POST /api/conversations/{id}/messages
# 发送新消息
Content-Type: application/json
{
  "content": "用户消息内容"
}
```

### 健康检查

```http
GET /api/health
# 服务器健康检查
```

## 数据库结构

### conversations 表

| 字段 | 类型 | 描述 |
|------|------|------|
| id | TEXT | 对话唯一标识符 |
| title | TEXT | 对话标题（可选） |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 最后更新时间 |
| metadata | TEXT | 元数据（JSON 格式） |

### messages 表

| 字段 | 类型 | 描述 |
|------|------|------|
| id | TEXT | 消息唯一标识符 |
| conversation_id | TEXT | 所属对话 ID |
| role | TEXT | 消息角色 (user/assistant/system/tool) |
| content | TEXT | 消息内容 |
| timestamp | DATETIME | 消息时间戳 |
| tool_calls | TEXT | 工具调用信息（JSON 格式） |
| tool_call_results | TEXT | 工具调用结果（JSON 格式） |

## 配置

### 环境变量

```bash
# API 密钥
export OPENROUTER_API_KEY="your-openrouter-key"
export OPENAI_API_KEY="your-openai-key"

# 日志级别
export RUST_LOG="minimal_agent=info,web_server=info"
```

### 数据库配置

默认使用 SQLite，数据库文件会自动创建。支持的 URL 格式：

```
sqlite:conversations.db          # 相对路径
sqlite:/path/to/conversations.db # 绝对路径
sqlite::memory:                  # 内存数据库（测试用）
```

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 使用不同端口
   cargo run --bin web_server -- --port 8080
   ```

2. **数据库权限问题**
   ```bash
   # 确保目录可写
   chmod 755 .
   # 或指定其他位置
   cargo run --bin web_server -- --database sqlite:/tmp/conversations.db
   ```

3. **API 密钥错误**
   ```bash
   # 检查环境变量
   echo $OPENROUTER_API_KEY
   # 或使用命令行参数
   cargo run --bin web_server -- --api-key "your-key"
   ```

4. **静态文件不显示**
   - 确保 `static/` 目录存在
   - 检查文件权限

### 调试模式

```bash
# 启用详细日志
RUST_LOG=debug cargo run --bin web_server

# 使用测试模式（更便宜的模型）
cargo run --bin web_server -- --test-mode
```

## 开发

### 添加新的 API 端点

1. 在 `src/web/handlers.rs` 中添加处理函数
2. 在 `src/web/server.rs` 中注册路由
3. 更新前端 JavaScript 调用新端点

### 自定义前端

静态文件位于 `static/` 目录：
- `index.html` - 主页面结构
- `style.css` - 样式表
- `script.js` - JavaScript 逻辑

### 数据库迁移

数据库会自动初始化，如需手动迁移：

```bash
sqlite3 conversations.db < migrations/001_initial.sql
```

## 部署

### 生产环境

```bash
# 构建发布版本
cargo build --release --bin web_server

# 运行
./target/release/web_server --port 80 --database sqlite:/var/lib/minimal_agent/conversations.db
```

### Docker 部署

```dockerfile
FROM rust:1.70 as builder
WORKDIR /app
COPY . .
RUN cargo build --release --bin web_server

FROM debian:bookworm-slim
RUN apt-get update && apt-get install -y ca-certificates && rm -rf /var/lib/apt/lists/*
COPY --from=builder /app/target/release/web_server /usr/local/bin/
COPY --from=builder /app/static /app/static
WORKDIR /app
EXPOSE 3000
CMD ["web_server"]
```

## 安全注意事项

1. **API 密钥**: 不要在客户端代码中暴露 API 密钥
2. **CORS**: 生产环境中配置适当的 CORS 策略
3. **HTTPS**: 生产环境使用 HTTPS
4. **数据库**: 定期备份对话数据
5. **访问控制**: 考虑添加身份验证机制
