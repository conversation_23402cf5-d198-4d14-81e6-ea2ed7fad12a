use clap::{Arg, Command};
use minimal_agent::{
    database::DatabaseRepository,
    tools::{ToolRegistry, builtin::{
        EchoTool, CalculatorTool, FileReadTool, FileWriteTool, FileDeleteTool,
        ListDirectoryTool, FileSearchTool, CodeAnalysisTool, ProjectStructureTool
    }},
    web::{create_app, start_server, AppState},
    AgentConfig, LlmConfig, prompts,
};
use std::sync::Arc;
use tracing::info;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt()
        .with_env_filter("minimal_agent=info,web_server=info")
        .init();

    // 解析命令行参数
    let matches = Command::new("Minimal Agent Web Server")
        .version("0.1.0")
        .about("Web interface for the minimal agent system")
        .arg(
            Arg::new("port")
                .short('p')
                .long("port")
                .value_name("PORT")
                .help("Port to run the web server on")
                .default_value("3000"),
        )
        .arg(
            Arg::new("database")
                .short('d')
                .long("database")
                .value_name("DATABASE_URL")
                .help("SQLite database URL")
                .default_value("sqlite:conversations.db"),
        )
        .arg(
            Arg::new("api-key")
                .long("api-key")
                .value_name("KEY")
                .help("API key (OpenAI or OpenRouter)")
                .required(false),
        )
        .arg(
            Arg::new("provider")
                .long("provider")
                .value_name("PROVIDER")
                .help("LLM provider (openai, openrouter)")
                .default_value("openrouter"),
        )
        .arg(
            Arg::new("model")
                .long("model")
                .value_name("MODEL")
                .help("LLM model to use")
                .default_value("anthropic/claude-3.5-sonnet"),
        )
        .arg(
            Arg::new("test-mode")
                .long("test-mode")
                .help("Use cheaper model for testing (Claude-3 Haiku)")
                .action(clap::ArgAction::SetTrue),
        )
        .arg(
            Arg::new("coding-mode")
                .long("coding-mode")
                .help("Enable coding assistant mode with specialized tools and prompts")
                .action(clap::ArgAction::SetTrue),
        )
        .get_matches();

    let port: u16 = matches.get_one::<String>("port").unwrap().parse()?;
    let database_url = matches.get_one::<String>("database").unwrap();
    let provider = matches.get_one::<String>("provider").unwrap().clone();
    let mut model = matches.get_one::<String>("model").unwrap().clone();
    let coding_mode = matches.get_flag("coding-mode");

    // 如果启用测试模式，使用更便宜的模型
    if matches.get_flag("test-mode") {
        model = match provider.as_str() {
            "openrouter" => "anthropic/claude-3-haiku".to_string(),
            "openai" => "gpt-3.5-turbo".to_string(),
            _ => model,
        };
        info!("🧪 测试模式: 使用模型 {}", model);
    }

    // 获取 API 密钥
    let api_key = matches
        .get_one::<String>("api-key")
        .cloned()
        .or_else(|| {
            match provider.as_str() {
                "openrouter" => std::env::var("OPENROUTER_API_KEY").ok(),
                "openai" => std::env::var("OPENAI_API_KEY").ok(),
                _ => None,
            }
        })
        .unwrap_or_else(|| {
            let env_var = match provider.as_str() {
                "openrouter" => "OPENROUTER_API_KEY",
                "openai" => "OPENAI_API_KEY",
                _ => "API_KEY",
            };
            eprintln!("Warning: No API key provided. Set {} environment variable or use --api-key", env_var);
            "your-api-key-here".to_string()
        });

    // 创建 Agent 配置
    let agent_config = AgentConfig {
        llm: LlmConfig {
            provider,
            model,
            api_key,
            base_url: None,
            temperature: Some(0.7),
            max_tokens: Some(4096),
        },
        max_iterations: 10,
        max_tool_calls_per_iteration: 5,
        system_prompt: Some(
            if coding_mode {
                prompts::get_system_prompt("coding").to_string()
            } else {
                prompts::get_system_prompt("general").to_string()
            }
        ),
    };

    // 创建工具注册表并注册内置工具
    let mut tool_registry = ToolRegistry::new();

    // 基础工具
    tool_registry.register_tool(Arc::new(EchoTool))?;
    tool_registry.register_tool(Arc::new(CalculatorTool))?;

    // 代码编写工具（在 coding-mode 下或默认启用）
    if coding_mode || true { // 默认启用文件工具
        tool_registry.register_tool(Arc::new(FileReadTool))?;
        tool_registry.register_tool(Arc::new(FileWriteTool))?;
        tool_registry.register_tool(Arc::new(FileDeleteTool))?;
        tool_registry.register_tool(Arc::new(ListDirectoryTool))?;
        tool_registry.register_tool(Arc::new(FileSearchTool))?;
        tool_registry.register_tool(Arc::new(CodeAnalysisTool))?;
        tool_registry.register_tool(Arc::new(ProjectStructureTool))?;
    }

    let tool_registry = Arc::new(tool_registry);

    info!("Registered {} tools", tool_registry.tool_count());
    if coding_mode {
        info!("🔧 Coding mode enabled with file editing and code analysis tools");
    }

    // 初始化数据库
    info!("Initializing database: {}", database_url);
    let db = DatabaseRepository::new(database_url).await?;

    // 创建应用状态
    let app_state = AppState::new(db, agent_config, tool_registry);

    // 创建应用
    let app = create_app(app_state);

    // 启动服务器
    start_server(app, port).await?;

    Ok(())
}
