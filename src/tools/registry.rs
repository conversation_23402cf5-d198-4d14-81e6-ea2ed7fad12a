use crate::tools::ToolTrait;
use crate::{AgentError, Result};
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::collections::HashMap;
use std::sync::Arc;

/// 工具定义，用于向 LLM 描述工具
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolDefinition {
    pub name: String,
    pub description: String,
    pub parameters: Value,
}

/// 工具包装器
#[derive(Clone)]
pub struct Tool {
    pub definition: ToolDefinition,
    pub implementation: Arc<dyn ToolTrait>,
}

/// 工具注册表，管理所有可用的工具
#[derive(Clone)]
pub struct ToolRegistry {
    tools: HashMap<String, Tool>,
}

impl ToolRegistry {
    /// 创建新的工具注册表
    pub fn new() -> Self {
        Self {
            tools: HashMap::new(),
        }
    }

    /// 注册工具
    pub fn register_tool(&mut self, tool: Arc<dyn ToolTrait>) -> Result<()> {
        let name = tool.name().to_string();
        let definition = ToolDefinition {
            name: name.clone(),
            description: tool.description().to_string(),
            parameters: tool.parameters_schema(),
        };

        let tool_wrapper = Tool {
            definition,
            implementation: tool,
        };

        self.tools.insert(name, tool_wrapper);
        Ok(())
    }

    /// 获取工具
    pub fn get_tool(&self, name: &str) -> Option<&Tool> {
        self.tools.get(name)
    }

    /// 获取所有工具定义（用于发送给 LLM）
    pub fn get_tool_definitions(&self) -> Vec<ToolDefinition> {
        self.tools.values().map(|tool| tool.definition.clone()).collect()
    }

    /// 获取所有工具名称
    pub fn get_tool_names(&self) -> Vec<String> {
        self.tools.keys().cloned().collect()
    }

    /// 检查工具是否存在
    pub fn has_tool(&self, name: &str) -> bool {
        self.tools.contains_key(name)
    }

    /// 移除工具
    pub fn remove_tool(&mut self, name: &str) -> Result<()> {
        if self.tools.remove(name).is_some() {
            Ok(())
        } else {
            Err(AgentError::tool_not_found(name))
        }
    }

    /// 获取工具数量
    pub fn tool_count(&self) -> usize {
        self.tools.len()
    }
}

impl Default for ToolRegistry {
    fn default() -> Self {
        Self::new()
    }
}

impl std::fmt::Debug for Tool {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("Tool")
            .field("definition", &self.definition)
            .finish()
    }
}

impl std::fmt::Debug for ToolRegistry {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("ToolRegistry")
            .field("tool_count", &self.tool_count())
            .field("tool_names", &self.get_tool_names())
            .finish()
    }
}
