use crate::tools::ToolTrait;
use crate::types::ToolExecutionResult;
use crate::{AgentError, Result};
use async_trait::async_trait;
use serde_json::{json, Value};
use std::fs;
use std::path::Path;
use tracing::{debug, warn};

/// 文件读取工具
pub struct FileReadTool;

#[async_trait]
impl ToolTrait for FileReadTool {
    fn name(&self) -> &str {
        "read_file"
    }

    fn description(&self) -> &str {
        "Read the contents of a file. Use this to examine existing code files, configuration files, or any text-based files."
    }

    fn parameters_schema(&self) -> Value {
        json!({
            "type": "object",
            "properties": {
                "file_path": {
                    "type": "string",
                    "description": "The path to the file to read (relative or absolute)"
                }
            },
            "required": ["file_path"]
        })
    }

    async fn execute(&self, parameters: Value) -> Result<ToolExecutionResult> {
        let file_path = parameters
            .get("file_path")
            .and_then(|v| v.as_str())
            .ok_or_else(|| AgentError::invalid_tool_parameters("Missing file_path parameter"))?;

        debug!("Reading file: {}", file_path);

        // 安全检查：防止读取敏感文件
        if is_sensitive_path(file_path) {
            return Ok(ToolExecutionResult::Error {
                error: "Access to this file is restricted for security reasons".to_string(),
            });
        }

        match fs::read_to_string(file_path) {
            Ok(content) => {
                let file_info = fs::metadata(file_path).ok();
                let file_size = file_info.as_ref().map(|m| m.len()).unwrap_or(0);
                
                Ok(ToolExecutionResult::Success {
                    output: json!({
                        "file_path": file_path,
                        "content": content,
                        "size_bytes": file_size,
                        "lines": content.lines().count(),
                        "encoding": "utf-8"
                    }),
                })
            }
            Err(e) => Ok(ToolExecutionResult::Error {
                error: format!("Failed to read file '{}': {}", file_path, e),
            }),
        }
    }
}

/// 文件写入工具
pub struct FileWriteTool;

#[async_trait]
impl ToolTrait for FileWriteTool {
    fn name(&self) -> &str {
        "write_file"
    }

    fn description(&self) -> &str {
        "Create a new file or overwrite an existing file with the provided content. Use this to create new code files or update existing ones."
    }

    fn parameters_schema(&self) -> Value {
        json!({
            "type": "object",
            "properties": {
                "file_path": {
                    "type": "string",
                    "description": "The path where the file should be created or updated"
                },
                "content": {
                    "type": "string",
                    "description": "The content to write to the file"
                },
                "create_dirs": {
                    "type": "boolean",
                    "description": "Whether to create parent directories if they don't exist",
                    "default": true
                }
            },
            "required": ["file_path", "content"]
        })
    }

    async fn execute(&self, parameters: Value) -> Result<ToolExecutionResult> {
        let file_path = parameters
            .get("file_path")
            .and_then(|v| v.as_str())
            .ok_or_else(|| AgentError::invalid_tool_parameters("Missing file_path parameter"))?;

        let content = parameters
            .get("content")
            .and_then(|v| v.as_str())
            .ok_or_else(|| AgentError::invalid_tool_parameters("Missing content parameter"))?;

        let create_dirs = parameters
            .get("create_dirs")
            .and_then(|v| v.as_bool())
            .unwrap_or(true);

        debug!("Writing file: {}", file_path);

        // 安全检查
        if is_sensitive_path(file_path) {
            return Ok(ToolExecutionResult::Error {
                error: "Writing to this location is restricted for security reasons".to_string(),
            });
        }

        let path = Path::new(file_path);
        
        // 创建父目录（如果需要）
        if create_dirs {
            if let Some(parent) = path.parent() {
                if let Err(e) = fs::create_dir_all(parent) {
                    return Ok(ToolExecutionResult::Error {
                        error: format!("Failed to create directories: {}", e),
                    });
                }
            }
        }

        match fs::write(file_path, content) {
            Ok(_) => {
                let file_info = fs::metadata(file_path).ok();
                let file_size = file_info.as_ref().map(|m| m.len()).unwrap_or(0);
                
                Ok(ToolExecutionResult::Success {
                    output: json!({
                        "file_path": file_path,
                        "size_bytes": file_size,
                        "lines": content.lines().count(),
                        "operation": "write",
                        "created_dirs": create_dirs
                    }),
                })
            }
            Err(e) => Ok(ToolExecutionResult::Error {
                error: format!("Failed to write file '{}': {}", file_path, e),
            }),
        }
    }
}

/// 文件删除工具
pub struct FileDeleteTool;

#[async_trait]
impl ToolTrait for FileDeleteTool {
    fn name(&self) -> &str {
        "delete_file"
    }

    fn description(&self) -> &str {
        "Delete a file or directory. Use with caution as this operation cannot be undone."
    }

    fn parameters_schema(&self) -> Value {
        json!({
            "type": "object",
            "properties": {
                "file_path": {
                    "type": "string",
                    "description": "The path to the file or directory to delete"
                },
                "recursive": {
                    "type": "boolean",
                    "description": "Whether to delete directories recursively",
                    "default": false
                }
            },
            "required": ["file_path"]
        })
    }

    async fn execute(&self, parameters: Value) -> Result<ToolExecutionResult> {
        let file_path = parameters
            .get("file_path")
            .and_then(|v| v.as_str())
            .ok_or_else(|| AgentError::invalid_tool_parameters("Missing file_path parameter"))?;

        let recursive = parameters
            .get("recursive")
            .and_then(|v| v.as_bool())
            .unwrap_or(false);

        debug!("Deleting file/directory: {}", file_path);

        // 安全检查
        if is_sensitive_path(file_path) {
            return Ok(ToolExecutionResult::Error {
                error: "Deleting this file/directory is restricted for security reasons".to_string(),
            });
        }

        let path = Path::new(file_path);
        
        if !path.exists() {
            return Ok(ToolExecutionResult::Error {
                error: format!("File or directory '{}' does not exist", file_path),
            });
        }

        let is_dir = path.is_dir();
        let result = if is_dir {
            if recursive {
                fs::remove_dir_all(file_path)
            } else {
                fs::remove_dir(file_path)
            }
        } else {
            fs::remove_file(file_path)
        };

        match result {
            Ok(_) => Ok(ToolExecutionResult::Success {
                output: json!({
                    "file_path": file_path,
                    "operation": "delete",
                    "was_directory": is_dir,
                    "recursive": recursive
                }),
            }),
            Err(e) => Ok(ToolExecutionResult::Error {
                error: format!("Failed to delete '{}': {}", file_path, e),
            }),
        }
    }
}

/// 目录列表工具
pub struct ListDirectoryTool;

#[async_trait]
impl ToolTrait for ListDirectoryTool {
    fn name(&self) -> &str {
        "list_directory"
    }

    fn description(&self) -> &str {
        "List the contents of a directory, showing files and subdirectories with their details."
    }

    fn parameters_schema(&self) -> Value {
        json!({
            "type": "object",
            "properties": {
                "directory_path": {
                    "type": "string",
                    "description": "The path to the directory to list",
                    "default": "."
                },
                "show_hidden": {
                    "type": "boolean",
                    "description": "Whether to show hidden files (starting with .)",
                    "default": false
                },
                "recursive": {
                    "type": "boolean",
                    "description": "Whether to list subdirectories recursively",
                    "default": false
                }
            }
        })
    }

    async fn execute(&self, parameters: Value) -> Result<ToolExecutionResult> {
        let directory_path = parameters
            .get("directory_path")
            .and_then(|v| v.as_str())
            .unwrap_or(".");

        let show_hidden = parameters
            .get("show_hidden")
            .and_then(|v| v.as_bool())
            .unwrap_or(false);

        let recursive = parameters
            .get("recursive")
            .and_then(|v| v.as_bool())
            .unwrap_or(false);

        debug!("Listing directory: {}", directory_path);

        if is_sensitive_path(directory_path) {
            return Ok(ToolExecutionResult::Error {
                error: "Access to this directory is restricted for security reasons".to_string(),
            });
        }

        match list_directory_contents(directory_path, show_hidden, recursive) {
            Ok(contents) => Ok(ToolExecutionResult::Success {
                output: json!({
                    "directory_path": directory_path,
                    "contents": contents,
                    "show_hidden": show_hidden,
                    "recursive": recursive
                }),
            }),
            Err(e) => Ok(ToolExecutionResult::Error {
                error: format!("Failed to list directory '{}': {}", directory_path, e),
            }),
        }
    }
}

/// 文件搜索工具
pub struct FileSearchTool;

#[async_trait]
impl ToolTrait for FileSearchTool {
    fn name(&self) -> &str {
        "search_files"
    }

    fn description(&self) -> &str {
        "Search for files by name pattern or content within a directory tree."
    }

    fn parameters_schema(&self) -> Value {
        json!({
            "type": "object",
            "properties": {
                "directory_path": {
                    "type": "string",
                    "description": "The directory to search in",
                    "default": "."
                },
                "name_pattern": {
                    "type": "string",
                    "description": "File name pattern to search for (supports wildcards like *.rs, *.py)"
                },
                "content_pattern": {
                    "type": "string",
                    "description": "Text pattern to search for within file contents"
                },
                "max_results": {
                    "type": "integer",
                    "description": "Maximum number of results to return",
                    "default": 50
                }
            }
        })
    }

    async fn execute(&self, parameters: Value) -> Result<ToolExecutionResult> {
        let directory_path = parameters
            .get("directory_path")
            .and_then(|v| v.as_str())
            .unwrap_or(".");

        let name_pattern = parameters
            .get("name_pattern")
            .and_then(|v| v.as_str());

        let content_pattern = parameters
            .get("content_pattern")
            .and_then(|v| v.as_str());

        let max_results = parameters
            .get("max_results")
            .and_then(|v| v.as_u64())
            .unwrap_or(50) as usize;

        debug!("Searching files in: {}", directory_path);

        if is_sensitive_path(directory_path) {
            return Ok(ToolExecutionResult::Error {
                error: "Searching in this directory is restricted for security reasons".to_string(),
            });
        }

        if name_pattern.is_none() && content_pattern.is_none() {
            return Ok(ToolExecutionResult::Error {
                error: "Either name_pattern or content_pattern must be provided".to_string(),
            });
        }

        match search_files(directory_path, name_pattern, content_pattern, max_results) {
            Ok(results) => Ok(ToolExecutionResult::Success {
                output: json!({
                    "directory_path": directory_path,
                    "name_pattern": name_pattern,
                    "content_pattern": content_pattern,
                    "results": results,
                    "total_found": results.len()
                }),
            }),
            Err(e) => Ok(ToolExecutionResult::Error {
                error: format!("Search failed: {}", e),
            }),
        }
    }
}

/// 列出目录内容的辅助函数
fn list_directory_contents(
    dir_path: &str,
    show_hidden: bool,
    recursive: bool,
) -> std::io::Result<Vec<Value>> {
    let mut contents = Vec::new();
    let path = Path::new(dir_path);

    if recursive {
        list_directory_recursive(path, show_hidden, &mut contents, 0, 3)?; // 限制递归深度
    } else {
        for entry in fs::read_dir(path)? {
            let entry = entry?;
            let file_name = entry.file_name().to_string_lossy().to_string();

            if !show_hidden && file_name.starts_with('.') {
                continue;
            }

            let metadata = entry.metadata()?;
            let file_type = if metadata.is_dir() {
                "directory"
            } else if metadata.is_file() {
                "file"
            } else {
                "other"
            };

            contents.push(json!({
                "name": file_name,
                "path": entry.path().to_string_lossy(),
                "type": file_type,
                "size": metadata.len(),
                "modified": metadata.modified().ok()
                    .and_then(|t| t.duration_since(std::time::UNIX_EPOCH).ok())
                    .map(|d| d.as_secs())
            }));
        }
    }

    Ok(contents)
}

/// 递归列出目录内容
fn list_directory_recursive(
    dir_path: &Path,
    show_hidden: bool,
    contents: &mut Vec<Value>,
    depth: usize,
    max_depth: usize,
) -> std::io::Result<()> {
    if depth > max_depth {
        return Ok(());
    }

    for entry in fs::read_dir(dir_path)? {
        let entry = entry?;
        let file_name = entry.file_name().to_string_lossy().to_string();

        if !show_hidden && file_name.starts_with('.') {
            continue;
        }

        let metadata = entry.metadata()?;
        let file_type = if metadata.is_dir() {
            "directory"
        } else if metadata.is_file() {
            "file"
        } else {
            "other"
        };

        contents.push(json!({
            "name": file_name,
            "path": entry.path().to_string_lossy(),
            "type": file_type,
            "size": metadata.len(),
            "depth": depth,
            "modified": metadata.modified().ok()
                .and_then(|t| t.duration_since(std::time::UNIX_EPOCH).ok())
                .map(|d| d.as_secs())
        }));

        if metadata.is_dir() {
            list_directory_recursive(&entry.path(), show_hidden, contents, depth + 1, max_depth)?;
        }
    }

    Ok(())
}

/// 搜索文件的辅助函数
fn search_files(
    dir_path: &str,
    name_pattern: Option<&str>,
    content_pattern: Option<&str>,
    max_results: usize,
) -> std::io::Result<Vec<Value>> {
    let mut results = Vec::new();
    let path = Path::new(dir_path);

    search_files_recursive(path, name_pattern, content_pattern, &mut results, max_results, 0, 5)?;

    Ok(results)
}

/// 递归搜索文件
fn search_files_recursive(
    dir_path: &Path,
    name_pattern: Option<&str>,
    content_pattern: Option<&str>,
    results: &mut Vec<Value>,
    max_results: usize,
    depth: usize,
    max_depth: usize,
) -> std::io::Result<()> {
    if depth > max_depth || results.len() >= max_results {
        return Ok(());
    }

    for entry in fs::read_dir(dir_path)? {
        let entry = entry?;
        let file_name = entry.file_name().to_string_lossy().to_string();
        let file_path = entry.path();

        if file_name.starts_with('.') {
            continue;
        }

        let metadata = entry.metadata()?;

        if metadata.is_dir() {
            search_files_recursive(&file_path, name_pattern, content_pattern, results, max_results, depth + 1, max_depth)?;
        } else if metadata.is_file() {
            let mut matches = false;

            // 检查文件名模式
            if let Some(pattern) = name_pattern {
                if matches_pattern(&file_name, pattern) {
                    matches = true;
                }
            }

            // 检查文件内容
            if let Some(content_pat) = content_pattern {
                if let Ok(content) = fs::read_to_string(&file_path) {
                    if content.contains(content_pat) {
                        matches = true;
                    }
                }
            }

            if matches {
                results.push(json!({
                    "name": file_name,
                    "path": file_path.to_string_lossy(),
                    "size": metadata.len(),
                    "modified": metadata.modified().ok()
                        .and_then(|t| t.duration_since(std::time::UNIX_EPOCH).ok())
                        .map(|d| d.as_secs())
                }));

                if results.len() >= max_results {
                    break;
                }
            }
        }
    }

    Ok(())
}

/// 简单的通配符模式匹配
fn matches_pattern(text: &str, pattern: &str) -> bool {
    if pattern == "*" {
        return true;
    }

    if pattern.contains('*') {
        let parts: Vec<&str> = pattern.split('*').collect();
        if parts.len() == 2 {
            let prefix = parts[0];
            let suffix = parts[1];
            return text.starts_with(prefix) && text.ends_with(suffix);
        }
    }

    text.contains(pattern)
}

/// 检查路径是否为敏感路径
fn is_sensitive_path(path: &str) -> bool {
    let sensitive_patterns = [
        "/etc/", "/sys/", "/proc/", "/dev/",
        "C:\\Windows\\", "C:\\System32\\",
        ".ssh/", ".aws/", ".env",
        "id_rsa", "id_ed25519", "private",
        "/root/", "/home/<USER>/.*",
    ];

    let path_lower = path.to_lowercase();

    for pattern in &sensitive_patterns {
        if path_lower.contains(&pattern.to_lowercase()) {
            warn!("Blocked access to sensitive path: {}", path);
            return true;
        }
    }

    // 检查是否试图访问上级目录
    if path.contains("../") || path.contains("..\\") {
        warn!("Blocked path traversal attempt: {}", path);
        return true;
    }

    false
}
