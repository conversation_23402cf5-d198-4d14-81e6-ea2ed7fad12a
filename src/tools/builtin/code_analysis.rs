use crate::tools::ToolTrait;
use crate::types::ToolExecutionResult;
use crate::{AgentError, Result};
use async_trait::async_trait;
use serde_json::{json, Value};
use std::collections::HashMap;
use std::fs;
use std::path::Path;

/// 代码分析工具
pub struct CodeAnalysisTool;

#[async_trait]
impl ToolTrait for CodeAnalysisTool {
    fn name(&self) -> &str {
        "analyze_code"
    }

    fn description(&self) -> &str {
        "Analyze code files to extract information about functions, classes, imports, and code structure."
    }

    fn parameters_schema(&self) -> Value {
        json!({
            "type": "object",
            "properties": {
                "file_path": {
                    "type": "string",
                    "description": "Path to the code file to analyze"
                },
                "language": {
                    "type": "string",
                    "description": "Programming language (auto-detected if not provided)",
                    "enum": ["rust", "python", "javascript", "typescript", "java", "cpp", "go", "auto"]
                }
            },
            "required": ["file_path"]
        })
    }

    async fn execute(&self, parameters: Value) -> Result<ToolExecutionResult> {
        let file_path = parameters
            .get("file_path")
            .and_then(|v| v.as_str())
            .ok_or_else(|| AgentError::invalid_tool_parameters("Missing file_path parameter"))?;

        let language = parameters
            .get("language")
            .and_then(|v| v.as_str())
            .unwrap_or("auto");

        match fs::read_to_string(file_path) {
            Ok(content) => {
                let detected_language = if language == "auto" {
                    detect_language(file_path)
                } else {
                    language.to_string()
                };

                let analysis = analyze_code_content(&content, &detected_language);

                Ok(ToolExecutionResult::Success {
                    output: json!({
                        "file_path": file_path,
                        "language": detected_language,
                        "analysis": analysis,
                        "lines_of_code": content.lines().count(),
                        "file_size": content.len()
                    }),
                })
            }
            Err(e) => Ok(ToolExecutionResult::Error {
                error: format!("Failed to read file '{}': {}", file_path, e),
            }),
        }
    }
}

/// 项目结构分析工具
pub struct ProjectStructureTool;

#[async_trait]
impl ToolTrait for ProjectStructureTool {
    fn name(&self) -> &str {
        "analyze_project_structure"
    }

    fn description(&self) -> &str {
        "Analyze the structure of a software project, identifying key files, dependencies, and project type."
    }

    fn parameters_schema(&self) -> Value {
        json!({
            "type": "object",
            "properties": {
                "project_path": {
                    "type": "string",
                    "description": "Path to the project root directory",
                    "default": "."
                }
            }
        })
    }

    async fn execute(&self, parameters: Value) -> Result<ToolExecutionResult> {
        let project_path = parameters
            .get("project_path")
            .and_then(|v| v.as_str())
            .unwrap_or(".");

        match analyze_project_structure(project_path) {
            Ok(structure) => Ok(ToolExecutionResult::Success {
                output: json!({
                    "project_path": project_path,
                    "structure": structure
                }),
            }),
            Err(e) => Ok(ToolExecutionResult::Error {
                error: format!("Failed to analyze project structure: {}", e),
            }),
        }
    }
}

/// 检测编程语言
fn detect_language(file_path: &str) -> String {
    let path = Path::new(file_path);
    if let Some(extension) = path.extension().and_then(|ext| ext.to_str()) {
        match extension.to_lowercase().as_str() {
            "rs" => "rust".to_string(),
            "py" => "python".to_string(),
            "js" => "javascript".to_string(),
            "ts" => "typescript".to_string(),
            "java" => "java".to_string(),
            "cpp" | "cc" | "cxx" | "c++" => "cpp".to_string(),
            "c" => "c".to_string(),
            "go" => "go".to_string(),
            "rb" => "ruby".to_string(),
            "php" => "php".to_string(),
            "cs" => "csharp".to_string(),
            _ => "unknown".to_string(),
        }
    } else {
        "unknown".to_string()
    }
}

/// 分析代码内容
fn analyze_code_content(content: &str, language: &str) -> Value {
    let mut analysis = json!({
        "functions": [],
        "classes": [],
        "imports": [],
        "comments": [],
        "todos": [],
        "complexity_estimate": "low"
    });

    let lines: Vec<&str> = content.lines().collect();
    
    match language {
        "rust" => analyze_rust_code(&lines, &mut analysis),
        "python" => analyze_python_code(&lines, &mut analysis),
        "javascript" | "typescript" => analyze_js_code(&lines, &mut analysis),
        "java" => analyze_java_code(&lines, &mut analysis),
        _ => analyze_generic_code(&lines, &mut analysis),
    }

    // 分析复杂度
    let complexity = estimate_complexity(&lines);
    analysis["complexity_estimate"] = json!(complexity);

    analysis
}

/// 分析 Rust 代码
fn analyze_rust_code(lines: &[&str], analysis: &mut Value) {
    let mut functions = Vec::new();
    let mut structs = Vec::new();
    let mut imports = Vec::new();

    for (i, line) in lines.iter().enumerate() {
        let trimmed = line.trim();
        
        // 函数定义
        if trimmed.starts_with("fn ") || trimmed.contains(" fn ") {
            if let Some(func_name) = extract_rust_function_name(trimmed) {
                functions.push(json!({
                    "name": func_name,
                    "line": i + 1,
                    "visibility": if trimmed.starts_with("pub") { "public" } else { "private" }
                }));
            }
        }
        
        // 结构体定义
        if trimmed.starts_with("struct ") || trimmed.contains(" struct ") {
            if let Some(struct_name) = extract_rust_struct_name(trimmed) {
                structs.push(json!({
                    "name": struct_name,
                    "line": i + 1,
                    "visibility": if trimmed.starts_with("pub") { "public" } else { "private" }
                }));
            }
        }
        
        // 导入语句
        if trimmed.starts_with("use ") {
            imports.push(json!({
                "statement": trimmed,
                "line": i + 1
            }));
        }
    }

    analysis["functions"] = json!(functions);
    analysis["structs"] = json!(structs);
    analysis["imports"] = json!(imports);
}

/// 分析 Python 代码
fn analyze_python_code(lines: &[&str], analysis: &mut Value) {
    let mut functions = Vec::new();
    let mut classes = Vec::new();
    let mut imports = Vec::new();

    for (i, line) in lines.iter().enumerate() {
        let trimmed = line.trim();
        
        // 函数定义
        if trimmed.starts_with("def ") {
            if let Some(func_name) = extract_python_function_name(trimmed) {
                functions.push(json!({
                    "name": func_name,
                    "line": i + 1,
                    "is_method": line.starts_with("    ") || line.starts_with("\t")
                }));
            }
        }
        
        // 类定义
        if trimmed.starts_with("class ") {
            if let Some(class_name) = extract_python_class_name(trimmed) {
                classes.push(json!({
                    "name": class_name,
                    "line": i + 1
                }));
            }
        }
        
        // 导入语句
        if trimmed.starts_with("import ") || trimmed.starts_with("from ") {
            imports.push(json!({
                "statement": trimmed,
                "line": i + 1
            }));
        }
    }

    analysis["functions"] = json!(functions);
    analysis["classes"] = json!(classes);
    analysis["imports"] = json!(imports);
}

/// 分析 JavaScript/TypeScript 代码
fn analyze_js_code(lines: &[&str], analysis: &mut Value) {
    let mut functions = Vec::new();
    let mut classes = Vec::new();
    let mut imports = Vec::new();

    for (i, line) in lines.iter().enumerate() {
        let trimmed = line.trim();
        
        // 函数定义
        if trimmed.contains("function ") || trimmed.contains("=> ") {
            if let Some(func_name) = extract_js_function_name(trimmed) {
                functions.push(json!({
                    "name": func_name,
                    "line": i + 1,
                    "type": if trimmed.contains("=>") { "arrow" } else { "function" }
                }));
            }
        }
        
        // 类定义
        if trimmed.starts_with("class ") {
            if let Some(class_name) = extract_js_class_name(trimmed) {
                classes.push(json!({
                    "name": class_name,
                    "line": i + 1
                }));
            }
        }
        
        // 导入语句
        if trimmed.starts_with("import ") || trimmed.starts_with("const ") && trimmed.contains("require(") {
            imports.push(json!({
                "statement": trimmed,
                "line": i + 1
            }));
        }
    }

    analysis["functions"] = json!(functions);
    analysis["classes"] = json!(classes);
    analysis["imports"] = json!(imports);
}

/// 分析 Java 代码
fn analyze_java_code(lines: &[&str], analysis: &mut Value) {
    let mut functions = Vec::new();
    let mut classes = Vec::new();
    let mut imports = Vec::new();

    for (i, line) in lines.iter().enumerate() {
        let trimmed = line.trim();
        
        // 方法定义
        if (trimmed.contains("public ") || trimmed.contains("private ") || trimmed.contains("protected ")) 
            && (trimmed.contains("(") && trimmed.contains(")")) 
            && !trimmed.contains("class ") {
            if let Some(method_name) = extract_java_method_name(trimmed) {
                functions.push(json!({
                    "name": method_name,
                    "line": i + 1,
                    "visibility": extract_java_visibility(trimmed)
                }));
            }
        }
        
        // 类定义
        if trimmed.contains("class ") || trimmed.contains("interface ") {
            if let Some(class_name) = extract_java_class_name(trimmed) {
                classes.push(json!({
                    "name": class_name,
                    "line": i + 1,
                    "type": if trimmed.contains("interface") { "interface" } else { "class" }
                }));
            }
        }
        
        // 导入语句
        if trimmed.starts_with("import ") {
            imports.push(json!({
                "statement": trimmed,
                "line": i + 1
            }));
        }
    }

    analysis["functions"] = json!(functions);
    analysis["classes"] = json!(classes);
    analysis["imports"] = json!(imports);
}

/// 通用代码分析
fn analyze_generic_code(lines: &[&str], analysis: &mut Value) {
    let mut comments = Vec::new();
    let mut todos = Vec::new();

    for (i, line) in lines.iter().enumerate() {
        let trimmed = line.trim();
        
        // 注释
        if trimmed.starts_with("//") || trimmed.starts_with("#") || trimmed.starts_with("/*") {
            comments.push(json!({
                "content": trimmed,
                "line": i + 1
            }));
        }
        
        // TODO 项目
        if trimmed.to_lowercase().contains("todo") || trimmed.to_lowercase().contains("fixme") {
            todos.push(json!({
                "content": trimmed,
                "line": i + 1
            }));
        }
    }

    analysis["comments"] = json!(comments);
    analysis["todos"] = json!(todos);
}

/// 估算代码复杂度
fn estimate_complexity(lines: &[&str]) -> String {
    let mut complexity_score = 0;
    
    for line in lines {
        let trimmed = line.trim();
        
        // 控制流语句增加复杂度
        if trimmed.contains("if ") || trimmed.contains("else") {
            complexity_score += 1;
        }
        if trimmed.contains("for ") || trimmed.contains("while ") {
            complexity_score += 2;
        }
        if trimmed.contains("match ") || trimmed.contains("switch ") {
            complexity_score += 2;
        }
        if trimmed.contains("try ") || trimmed.contains("catch ") {
            complexity_score += 1;
        }
    }
    
    let lines_count = lines.len();
    let complexity_ratio = complexity_score as f64 / lines_count as f64;
    
    if complexity_ratio > 0.3 {
        "high".to_string()
    } else if complexity_ratio > 0.15 {
        "medium".to_string()
    } else {
        "low".to_string()
    }
}

// 辅助函数用于提取函数/类名称
fn extract_rust_function_name(line: &str) -> Option<String> {
    if let Some(start) = line.find("fn ") {
        let after_fn = &line[start + 3..];
        if let Some(end) = after_fn.find('(') {
            return Some(after_fn[..end].trim().to_string());
        }
    }
    None
}

fn extract_rust_struct_name(line: &str) -> Option<String> {
    if let Some(start) = line.find("struct ") {
        let after_struct = &line[start + 7..];
        let name = after_struct.split_whitespace().next()?;
        return Some(name.to_string());
    }
    None
}

fn extract_python_function_name(line: &str) -> Option<String> {
    if let Some(start) = line.find("def ") {
        let after_def = &line[start + 4..];
        if let Some(end) = after_def.find('(') {
            return Some(after_def[..end].trim().to_string());
        }
    }
    None
}

fn extract_python_class_name(line: &str) -> Option<String> {
    if let Some(start) = line.find("class ") {
        let after_class = &line[start + 6..];
        let name = after_class.split(['(', ':']).next()?.trim();
        return Some(name.to_string());
    }
    None
}

fn extract_js_function_name(line: &str) -> Option<String> {
    if line.contains("function ") {
        if let Some(start) = line.find("function ") {
            let after_fn = &line[start + 9..];
            if let Some(end) = after_fn.find('(') {
                return Some(after_fn[..end].trim().to_string());
            }
        }
    } else if line.contains("=>") {
        // Arrow function
        if let Some(arrow_pos) = line.find("=>") {
            let before_arrow = &line[..arrow_pos];
            if let Some(eq_pos) = before_arrow.rfind('=') {
                let var_part = &before_arrow[..eq_pos];
                if let Some(name) = var_part.split_whitespace().last() {
                    return Some(name.to_string());
                }
            }
        }
    }
    None
}

fn extract_js_class_name(line: &str) -> Option<String> {
    if let Some(start) = line.find("class ") {
        let after_class = &line[start + 6..];
        let name = after_class.split_whitespace().next()?;
        return Some(name.to_string());
    }
    None
}

fn extract_java_method_name(line: &str) -> Option<String> {
    if let Some(paren_pos) = line.find('(') {
        let before_paren = &line[..paren_pos];
        let parts: Vec<&str> = before_paren.split_whitespace().collect();
        return parts.last().map(|s| s.to_string());
    }
    None
}

fn extract_java_class_name(line: &str) -> Option<String> {
    let keywords = ["class ", "interface "];
    for keyword in &keywords {
        if let Some(start) = line.find(keyword) {
            let after_keyword = &line[start + keyword.len()..];
            let name = after_keyword.split_whitespace().next()?;
            return Some(name.to_string());
        }
    }
    None
}

fn extract_java_visibility(line: &str) -> String {
    if line.contains("public ") {
        "public".to_string()
    } else if line.contains("private ") {
        "private".to_string()
    } else if line.contains("protected ") {
        "protected".to_string()
    } else {
        "package".to_string()
    }
}

/// 分析项目结构
fn analyze_project_structure(project_path: &str) -> std::io::Result<Value> {
    let path = Path::new(project_path);
    let mut structure = json!({
        "project_type": "unknown",
        "languages": [],
        "config_files": [],
        "source_directories": [],
        "dependencies": {}
    });

    // 检测项目类型
    let project_type = detect_project_type(path)?;
    structure["project_type"] = json!(project_type);

    // 分析语言分布
    let languages = analyze_language_distribution(path)?;
    structure["languages"] = json!(languages);

    // 查找配置文件
    let config_files = find_config_files(path)?;
    structure["config_files"] = json!(config_files);

    // 查找源代码目录
    let source_dirs = find_source_directories(path)?;
    structure["source_directories"] = json!(source_dirs);

    Ok(structure)
}

/// 检测项目类型
fn detect_project_type(path: &Path) -> std::io::Result<String> {
    let entries = fs::read_dir(path)?;
    
    for entry in entries {
        let entry = entry?;
        let file_name = entry.file_name().to_string_lossy().to_string();
        
        match file_name.as_str() {
            "Cargo.toml" => return Ok("rust".to_string()),
            "package.json" => return Ok("nodejs".to_string()),
            "requirements.txt" | "setup.py" | "pyproject.toml" => return Ok("python".to_string()),
            "pom.xml" | "build.gradle" => return Ok("java".to_string()),
            "go.mod" => return Ok("go".to_string()),
            "Makefile" => return Ok("c/cpp".to_string()),
            _ => {}
        }
    }
    
    Ok("unknown".to_string())
}

/// 分析语言分布
fn analyze_language_distribution(path: &Path) -> std::io::Result<Vec<Value>> {
    let mut language_counts: HashMap<String, usize> = HashMap::new();
    
    analyze_directory_languages(path, &mut language_counts, 0, 3)?;
    
    let mut languages: Vec<Value> = language_counts
        .into_iter()
        .map(|(lang, count)| json!({
            "language": lang,
            "file_count": count
        }))
        .collect();
    
    languages.sort_by(|a, b| {
        b["file_count"].as_u64().unwrap_or(0)
            .cmp(&a["file_count"].as_u64().unwrap_or(0))
    });
    
    Ok(languages)
}

/// 递归分析目录中的语言
fn analyze_directory_languages(
    path: &Path,
    language_counts: &mut HashMap<String, usize>,
    depth: usize,
    max_depth: usize,
) -> std::io::Result<()> {
    if depth > max_depth {
        return Ok(());
    }

    for entry in fs::read_dir(path)? {
        let entry = entry?;
        let file_name = entry.file_name().to_string_lossy().to_string();
        
        if file_name.starts_with('.') {
            continue;
        }

        let file_path = entry.path();
        
        if file_path.is_dir() {
            analyze_directory_languages(&file_path, language_counts, depth + 1, max_depth)?;
        } else if file_path.is_file() {
            let language = detect_language(&file_path.to_string_lossy());
            if language != "unknown" {
                *language_counts.entry(language).or_insert(0) += 1;
            }
        }
    }

    Ok(())
}

/// 查找配置文件
fn find_config_files(path: &Path) -> std::io::Result<Vec<String>> {
    let config_patterns = [
        "Cargo.toml", "package.json", "requirements.txt", "setup.py",
        "pom.xml", "build.gradle", "go.mod", "Makefile", "CMakeLists.txt",
        ".gitignore", "README.md", "LICENSE", "Dockerfile",
        "tsconfig.json", "webpack.config.js", ".eslintrc", "pyproject.toml"
    ];

    let mut config_files = Vec::new();
    
    for entry in fs::read_dir(path)? {
        let entry = entry?;
        let file_name = entry.file_name().to_string_lossy().to_string();
        
        if config_patterns.contains(&file_name.as_str()) {
            config_files.push(file_name);
        }
    }
    
    Ok(config_files)
}

/// 查找源代码目录
fn find_source_directories(path: &Path) -> std::io::Result<Vec<String>> {
    let source_dir_names = ["src", "lib", "source", "app", "components", "modules", "pkg"];
    let mut source_dirs = Vec::new();
    
    for entry in fs::read_dir(path)? {
        let entry = entry?;
        let file_name = entry.file_name().to_string_lossy().to_string();
        
        if entry.path().is_dir() && source_dir_names.contains(&file_name.as_str()) {
            source_dirs.push(file_name);
        }
    }
    
    Ok(source_dirs)
}
