use crate::tools::ToolTrait;
use crate::types::ToolExecutionResult;
use crate::Result;
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};

/// Editor 工具 - 允许 Agent 与编辑器交互
pub struct EditorTool;

#[derive(Debug, Serialize, Deserialize)]
struct EditorParameters {
    action: String,
    file_path: Option<String>,
    content: Option<String>,
    language: Option<String>,
}

#[async_trait]
impl ToolTrait for EditorTool {
    fn name(&self) -> &str {
        "editor"
    }

    fn description(&self) -> &str {
        "与代码编辑器交互，可以创建、编辑、保存文件，以及在编辑器中显示代码"
    }

    fn parameters_schema(&self) -> Value {
        json!({
            "type": "object",
            "properties": {
                "action": {
                    "type": "string",
                    "enum": ["create_file", "open_file", "save_file", "show_code", "set_content"],
                    "description": "要执行的操作"
                },
                "file_path": {
                    "type": "string",
                    "description": "文件路径（相对路径）"
                },
                "content": {
                    "type": "string",
                    "description": "文件内容或要显示的代码"
                },
                "language": {
                    "type": "string",
                    "description": "编程语言（用于语法高亮）"
                }
            },
            "required": ["action"]
        })
    }

    async fn execute(&self, parameters: Value) -> Result<ToolExecutionResult> {
        let params: EditorParameters = serde_json::from_value(parameters)
            .map_err(|e| crate::AgentError::invalid_tool_parameters(e.to_string()))?;

        let result = match params.action.as_str() {
            "create_file" => self.create_file(params).await?,
            "open_file" => self.open_file(params).await?,
            "save_file" => self.save_file(params).await?,
            "show_code" => self.show_code(params).await?,
            "set_content" => self.set_content(params).await?,
            _ => return Err(crate::AgentError::tool_execution(format!(
                "Unknown action: {}",
                params.action
            ))),
        };

        Ok(ToolExecutionResult::Success { output: result })
    }
}

impl EditorTool {
    /// 创建新文件
    async fn create_file(&self, params: EditorParameters) -> Result<Value> {
        let file_path = params.file_path.ok_or_else(|| {
            crate::AgentError::invalid_tool_parameters("file_path is required for create_file")
        })?;

        let content = params.content.unwrap_or_default();

        // 安全检查
        if file_path.contains("..") || file_path.starts_with('/') {
            return Err(crate::AgentError::tool_execution("Invalid file path"));
        }

        // 确保父目录存在
        if let Some(parent) = std::path::Path::new(&file_path).parent() {
            if let Err(e) = std::fs::create_dir_all(parent) {
                return Err(crate::AgentError::tool_execution(format!(
                    "Failed to create parent directory: {}",
                    e
                )));
            }
        }

        match std::fs::write(&file_path, &content) {
            Ok(_) => Ok(json!({
                "success": true,
                "message": format!("Created file: {}", file_path),
                "file_path": file_path,
                "content": content
            })),
            Err(e) => Err(crate::AgentError::tool_execution(format!(
                "Failed to create file: {}",
                e
            ))),
        }
    }

    /// 打开文件
    async fn open_file(&self, params: EditorParameters) -> Result<Value> {
        let file_path = params.file_path.ok_or_else(|| {
            crate::AgentError::invalid_tool_parameters("file_path is required for open_file")
        })?;

        // 安全检查
        if file_path.contains("..") || file_path.starts_with('/') {
            return Err(crate::AgentError::tool_execution("Invalid file path"));
        }

        match std::fs::read_to_string(&file_path) {
            Ok(content) => {
                let language = self.detect_language(&file_path);
                Ok(json!({
                    "success": true,
                    "message": format!("Opened file: {}", file_path),
                    "file_path": file_path,
                    "content": content,
                    "language": language
                }))
            }
            Err(e) => Err(crate::AgentError::tool_execution(format!(
                "Failed to open file: {}",
                e
            ))),
        }
    }

    /// 保存文件
    async fn save_file(&self, params: EditorParameters) -> Result<Value> {
        let file_path = params.file_path.ok_or_else(|| {
            crate::AgentError::invalid_tool_parameters("file_path is required for save_file")
        })?;

        let content = params.content.ok_or_else(|| {
            crate::AgentError::invalid_tool_parameters("content is required for save_file")
        })?;

        // 安全检查
        if file_path.contains("..") || file_path.starts_with('/') {
            return Err(crate::AgentError::tool_execution("Invalid file path"));
        }

        match std::fs::write(&file_path, &content) {
            Ok(_) => Ok(json!({
                "success": true,
                "message": format!("Saved file: {}", file_path),
                "file_path": file_path
            })),
            Err(e) => Err(crate::AgentError::tool_execution(format!(
                "Failed to save file: {}",
                e
            ))),
        }
    }

    /// 在编辑器中显示代码
    async fn show_code(&self, params: EditorParameters) -> Result<Value> {
        let content = params.content.ok_or_else(|| {
            crate::AgentError::invalid_tool_parameters("content is required for show_code")
        })?;

        let language = params.language.unwrap_or_else(|| "plaintext".to_string());

        Ok(json!({
            "success": true,
            "message": "Code displayed in editor",
            "content": content,
            "language": language,
            "action": "show_code"
        }))
    }

    /// 设置编辑器内容
    async fn set_content(&self, params: EditorParameters) -> Result<Value> {
        let content = params.content.ok_or_else(|| {
            crate::AgentError::invalid_tool_parameters("content is required for set_content")
        })?;

        let language = params.language.unwrap_or_else(|| "plaintext".to_string());
        let file_path = params.file_path;

        Ok(json!({
            "success": true,
            "message": "Editor content updated",
            "content": content,
            "language": language,
            "file_path": file_path,
            "action": "set_content"
        }))
    }

    /// 检测文件语言
    fn detect_language(&self, file_path: &str) -> String {
        let extension = std::path::Path::new(file_path)
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("");

        match extension.to_lowercase().as_str() {
            "rs" => "rust",
            "py" => "python",
            "js" => "javascript",
            "ts" => "typescript",
            "html" => "html",
            "css" => "css",
            "json" => "json",
            "toml" => "toml",
            "yaml" | "yml" => "yaml",
            "md" => "markdown",
            "txt" => "plaintext",
            "sh" => "shell",
            "sql" => "sql",
            "go" => "go",
            "java" => "java",
            "cpp" | "cc" | "cxx" => "cpp",
            "c" => "c",
            "h" | "hpp" => "c",
            _ => "plaintext",
        }
        .to_string()
    }
}
