use crate::database::models::*;
use crate::web::state::AppState;
use crate::{Agent, Message};
use axum::{
    extract::{Path, State, Query},
    http::StatusCode,
    response::J<PERSON>,
};
use serde_json::json;
use tracing::{debug, error, info};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 获取所有对话列表
pub async fn get_conversations(
    State(state): State<AppState>,
) -> Result<Json<ApiResponse<Vec<ConversationInfo>>>, StatusCode> {
    debug!("GET /api/conversations");
    
    match state.db.get_conversations().await {
        Ok(conversations) => {
            info!("Retrieved {} conversations", conversations.len());
            Ok(Json(ApiResponse::success(conversations)))
        }
        Err(e) => {
            error!("Failed to get conversations: {}", e);
            Ok(Json(ApiResponse::error(e.to_string())))
        }
    }
}

/// 创建新对话
pub async fn create_conversation(
    State(state): State<AppState>,
    Json(request): <PERSON><PERSON><CreateConversationRequest>,
) -> Result<Json<ApiResponse<String>>, StatusCode> {
    debug!("POST /api/conversations");
    
    match state.db.create_conversation(request.title).await {
        Ok(conversation_id) => {
            info!("Created conversation: {}", conversation_id);
            Ok(Json(ApiResponse::success(conversation_id)))
        }
        Err(e) => {
            error!("Failed to create conversation: {}", e);
            Ok(Json(ApiResponse::error(e.to_string())))
        }
    }
}

/// 获取对话的消息历史
pub async fn get_conversation_messages(
    State(state): State<AppState>,
    Path(conversation_id): Path<String>,
) -> Result<Json<ApiResponse<Vec<WebMessage>>>, StatusCode> {
    debug!("GET /api/conversations/{}/messages", conversation_id);
    
    match state.db.get_messages(&conversation_id).await {
        Ok(messages) => {
            info!("Retrieved {} messages for conversation {}", messages.len(), conversation_id);
            Ok(Json(ApiResponse::success(messages)))
        }
        Err(e) => {
            error!("Failed to get messages: {}", e);
            Ok(Json(ApiResponse::error(e.to_string())))
        }
    }
}

/// 发送消息并获取 Agent 响应
pub async fn send_message(
    State(state): State<AppState>,
    Path(conversation_id): Path<String>,
    Json(request): Json<SendMessageRequest>,
) -> Result<Json<ApiResponse<WebMessage>>, StatusCode> {
    debug!("POST /api/conversations/{}/messages", conversation_id);
    info!("Processing message: {}", request.content);
    
    // 验证对话是否存在
    match state.db.get_conversation(&conversation_id).await {
        Ok(Some(_)) => {},
        Ok(None) => {
            error!("Conversation not found: {}", conversation_id);
            return Ok(Json(ApiResponse::error("Conversation not found".to_string())));
        }
        Err(e) => {
            error!("Failed to check conversation: {}", e);
            return Ok(Json(ApiResponse::error(e.to_string())));
        }
    }

    // 创建用户消息
    let user_message = Message::new_user(request.content.clone());
    
    // 保存用户消息到数据库
    if let Err(e) = state.db.save_message(&conversation_id, &user_message).await {
        error!("Failed to save user message: {}", e);
        return Ok(Json(ApiResponse::error(e.to_string())));
    }

    // 创建 Agent 实例并处理消息
    let mut agent = Agent::new(state.agent_config.clone(), (*state.tool_registry).clone());
    agent.start_conversation();

    // 加载历史消息到 Agent
    match state.db.get_messages(&conversation_id).await {
        Ok(messages) => {
            // 将历史消息转换为 Agent 消息格式并添加到对话中
            for web_msg in messages {
                if web_msg.id != user_message.id.to_string() { // 跳过刚刚添加的用户消息
                    let agent_message = convert_web_message_to_agent_message(web_msg);
                    if let Err(e) = agent.add_message_to_current_conversation(agent_message) {
                        error!("Failed to add historical message to agent: {}", e);
                    }
                }
            }
        }
        Err(e) => {
            error!("Failed to load conversation history: {}", e);
        }
    }

    // 处理用户输入
    match agent.process_user_input(request.content).await {
        Ok(response_content) => {
            // 创建 Assistant 响应消息
            let assistant_message = Message::new_assistant(response_content);
            
            // 保存 Assistant 消息到数据库
            if let Err(e) = state.db.save_message(&conversation_id, &assistant_message).await {
                error!("Failed to save assistant message: {}", e);
                return Ok(Json(ApiResponse::error(e.to_string())));
            }

            // 转换为 Web 消息格式返回
            let web_message = WebMessage {
                id: assistant_message.id.to_string(),
                role: "assistant".to_string(),
                content: assistant_message.content,
                timestamp: assistant_message.timestamp,
                tool_calls: assistant_message.tool_calls.map(|calls| serde_json::to_value(calls).unwrap_or_default()),
                tool_call_results: assistant_message.tool_call_results.map(|results| serde_json::to_value(results).unwrap_or_default()),
                tool_call_id: assistant_message.tool_call_id,
            };

            info!("Successfully processed message for conversation {}", conversation_id);
            Ok(Json(ApiResponse::success(web_message)))
        }
        Err(e) => {
            error!("Agent failed to process message: {}", e);
            Ok(Json(ApiResponse::error(e.to_string())))
        }
    }
}

/// 删除对话
pub async fn delete_conversation(
    State(state): State<AppState>,
    Path(conversation_id): Path<String>,
) -> Result<Json<ApiResponse<()>>, StatusCode> {
    debug!("DELETE /api/conversations/{}", conversation_id);
    
    match state.db.delete_conversation(&conversation_id).await {
        Ok(_) => {
            info!("Deleted conversation: {}", conversation_id);
            Ok(Json(ApiResponse::success(())))
        }
        Err(e) => {
            error!("Failed to delete conversation: {}", e);
            Ok(Json(ApiResponse::error(e.to_string())))
        }
    }
}

/// 健康检查端点
pub async fn health_check() -> Json<serde_json::Value> {
    Json(json!({
        "status": "healthy",
        "timestamp": chrono::Utc::now().to_rfc3339()
    }))
}



/// 将 WebMessage 转换为 Agent Message
fn convert_web_message_to_agent_message(web_msg: WebMessage) -> Message {
    use crate::types::MessageRole;
    use uuid::Uuid;

    let role = match web_msg.role.as_str() {
        "user" => MessageRole::User,
        "assistant" => MessageRole::Assistant,
        "system" => MessageRole::System,
        "tool" => MessageRole::Tool,
        _ => MessageRole::User,
    };

    Message {
        id: Uuid::parse_str(&web_msg.id).unwrap_or_else(|_| Uuid::new_v4()),
        role,
        content: web_msg.content,
        timestamp: web_msg.timestamp,
        tool_calls: web_msg.tool_calls.and_then(|v| serde_json::from_value(v).ok()),
        tool_call_results: web_msg.tool_call_results.and_then(|v| serde_json::from_value(v).ok()),
        tool_call_id: web_msg.tool_call_id,
    }
}

// 文件操作相关的数据结构
#[derive(Serialize, Deserialize)]
pub struct FileItem {
    pub name: String,
    pub path: String,
    pub r#type: String, // "file" or "folder"
    pub children: Option<Vec<FileItem>>,
}

#[derive(Serialize, Deserialize)]
pub struct FileContent {
    pub content: String,
    pub language: Option<String>,
}

#[derive(Serialize, Deserialize)]
pub struct CreateFileRequest {
    pub path: String,
    pub content: Option<String>,
    pub r#type: String, // "file" or "folder"
}

#[derive(Serialize, Deserialize)]
pub struct UpdateFileRequest {
    pub content: String,
}

#[derive(Serialize, Deserialize)]
pub struct RunFileRequest {
    pub path: String,
}

/// 获取文件树
pub async fn get_file_tree(
    Query(params): Query<HashMap<String, String>>,
) -> Result<Json<ApiResponse<Vec<FileItem>>>, StatusCode> {
    debug!("GET /api/files");

    let root_path = params.get("path").map(|s| s.as_str()).unwrap_or(".");

    // 安全检查：防止路径遍历攻击
    if root_path.contains("..") || (root_path.starts_with('/') && root_path != "/") {
        error!("Invalid directory path: {}", root_path);
        return Ok(Json(ApiResponse::error("Invalid directory path".to_string())));
    }

    match build_file_tree(root_path) {
        Ok(tree) => {
            info!("Retrieved file tree for '{}' with {} items", root_path, tree.len());
            Ok(Json(ApiResponse::success(tree)))
        }
        Err(e) => {
            error!("Failed to get file tree for '{}': {}", root_path, e);
            Ok(Json(ApiResponse::error(e.to_string())))
        }
    }
}

/// 获取文件内容
pub async fn get_file_content(
    Path(file_path): Path<String>,
) -> Result<Json<ApiResponse<FileContent>>, StatusCode> {
    debug!("GET /api/files/{}", file_path);

    // 安全检查：防止路径遍历攻击
    if file_path.contains("..") || file_path.starts_with('/') {
        error!("Invalid file path: {}", file_path);
        return Ok(Json(ApiResponse::error("Invalid file path".to_string())));
    }

    match std::fs::read_to_string(&file_path) {
        Ok(content) => {
            info!("Read file: {}", file_path);
            let file_content = FileContent {
                content,
                language: detect_language_from_path(&file_path),
            };
            Ok(Json(ApiResponse::success(file_content)))
        }
        Err(e) => {
            error!("Failed to read file {}: {}", file_path, e);
            Ok(Json(ApiResponse::error(e.to_string())))
        }
    }
}

/// 创建文件或文件夹
pub async fn create_file(
    Json(request): Json<CreateFileRequest>,
) -> Result<Json<ApiResponse<()>>, StatusCode> {
    debug!("POST /api/files - creating {}: {}", request.r#type, request.path);

    // 安全检查
    if request.path.contains("..") || request.path.starts_with('/') {
        error!("Invalid file path: {}", request.path);
        return Ok(Json(ApiResponse::error("Invalid file path".to_string())));
    }

    let result = if request.r#type == "folder" {
        std::fs::create_dir_all(&request.path)
    } else {
        // 确保父目录存在
        if let Some(parent) = std::path::Path::new(&request.path).parent() {
            if let Err(e) = std::fs::create_dir_all(parent) {
                error!("Failed to create parent directory: {}", e);
                return Ok(Json(ApiResponse::error(e.to_string())));
            }
        }
        std::fs::write(&request.path, request.content.unwrap_or_default())
    };

    match result {
        Ok(_) => {
            info!("Created {}: {}", request.r#type, request.path);
            Ok(Json(ApiResponse::success(())))
        }
        Err(e) => {
            error!("Failed to create {}: {}", request.r#type, e);
            Ok(Json(ApiResponse::error(e.to_string())))
        }
    }
}

/// 更新文件内容
pub async fn update_file(
    Path(file_path): Path<String>,
    Json(request): Json<UpdateFileRequest>,
) -> Result<Json<ApiResponse<()>>, StatusCode> {
    debug!("PUT /api/files/{}", file_path);

    // 安全检查
    if file_path.contains("..") || file_path.starts_with('/') {
        error!("Invalid file path: {}", file_path);
        return Ok(Json(ApiResponse::error("Invalid file path".to_string())));
    }

    match std::fs::write(&file_path, &request.content) {
        Ok(_) => {
            info!("Updated file: {}", file_path);
            Ok(Json(ApiResponse::success(())))
        }
        Err(e) => {
            error!("Failed to update file {}: {}", file_path, e);
            Ok(Json(ApiResponse::error(e.to_string())))
        }
    }
}

/// 运行文件（简单实现）
pub async fn run_file(
    Json(request): Json<RunFileRequest>,
) -> Result<Json<ApiResponse<String>>, StatusCode> {
    debug!("POST /api/files/run - running: {}", request.path);

    // 安全检查
    if request.path.contains("..") || request.path.starts_with('/') {
        error!("Invalid file path: {}", request.path);
        return Ok(Json(ApiResponse::error("Invalid file path".to_string())));
    }

    // 根据文件扩展名决定如何运行
    let extension = std::path::Path::new(&request.path)
        .extension()
        .and_then(|ext| ext.to_str())
        .unwrap_or("");

    let command = match extension {
        "py" => format!("python3 {}", request.path),
        "js" => format!("node {}", request.path),
        "rs" => format!("rustc {} && ./{}", request.path, request.path.replace(".rs", "")),
        _ => {
            return Ok(Json(ApiResponse::error("Unsupported file type".to_string())));
        }
    };

    match std::process::Command::new("sh")
        .arg("-c")
        .arg(&command)
        .output()
    {
        Ok(output) => {
            let result = if output.status.success() {
                String::from_utf8_lossy(&output.stdout).to_string()
            } else {
                format!("Error: {}", String::from_utf8_lossy(&output.stderr))
            };

            info!("Executed file: {} with result: {}", request.path, result);
            Ok(Json(ApiResponse::success(result)))
        }
        Err(e) => {
            error!("Failed to run file {}: {}", request.path, e);
            Ok(Json(ApiResponse::error(e.to_string())))
        }
    }
}

// 辅助函数

/// 构建文件树
fn build_file_tree(dir_path: &str) -> Result<Vec<FileItem>, std::io::Error> {
    let mut items = Vec::new();
    let entries = std::fs::read_dir(dir_path)?;

    for entry in entries {
        let entry = entry?;
        let path = entry.path();
        let name = path.file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("unknown")
            .to_string();

        // 跳过隐藏文件和特定目录
        if name.starts_with('.') || name == "target" || name == "node_modules" {
            continue;
        }

        let path_str = path.to_string_lossy().to_string();

        if path.is_dir() {
            let children = build_file_tree(&path_str).ok();
            items.push(FileItem {
                name,
                path: path_str,
                r#type: "folder".to_string(),
                children,
            });
        } else {
            items.push(FileItem {
                name,
                path: path_str,
                r#type: "file".to_string(),
                children: None,
            });
        }
    }

    // 排序：文件夹在前，然后按名称排序
    items.sort_by(|a, b| {
        match (a.r#type.as_str(), b.r#type.as_str()) {
            ("folder", "file") => std::cmp::Ordering::Less,
            ("file", "folder") => std::cmp::Ordering::Greater,
            _ => a.name.cmp(&b.name),
        }
    });

    Ok(items)
}

/// 根据文件路径检测语言
fn detect_language_from_path(path: &str) -> Option<String> {
    let extension = std::path::Path::new(path)
        .extension()
        .and_then(|ext| ext.to_str())?;

    let language = match extension.to_lowercase().as_str() {
        "rs" => "rust",
        "py" => "python",
        "js" => "javascript",
        "ts" => "typescript",
        "html" => "html",
        "css" => "css",
        "json" => "json",
        "toml" => "toml",
        "yaml" | "yml" => "yaml",
        "md" => "markdown",
        "txt" => "plaintext",
        "sh" => "shell",
        "sql" => "sql",
        "go" => "go",
        "java" => "java",
        "cpp" | "cc" | "cxx" => "cpp",
        "c" => "c",
        "h" | "hpp" => "c",
        _ => "plaintext",
    };

    Some(language.to_string())
}
