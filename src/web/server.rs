use crate::web::{handlers::*, state::AppState};
use axum::{
    routing::{delete, get, post, put},
    Router,
};
use tower::ServiceBuilder;
use tower_http::{
    cors::{Any, CorsLayer},
    services::ServeDir,
};
use tracing::info;

/// 创建 Web 应用路由
pub fn create_app(state: AppState) -> Router {
    // API 路由
    let api_routes = Router::new()
        .route("/health", get(health_check))
        .route("/conversations", get(get_conversations))
        .route("/conversations", post(create_conversation))
        .route("/conversations/:id/messages", get(get_conversation_messages))
        .route("/conversations/:id/messages", post(send_message))
        .route("/conversations/:id", delete(delete_conversation))
        // 文件操作路由
        .route("/files", get(get_file_tree))
        .route("/files", post(create_file))
        .route("/files/*path", get(get_file_content))
        .route("/files/*path", put(update_file))
        .route("/files/run", post(run_file))
        .with_state(state);

    // 静态文件服务
    let static_files = ServeDir::new("static").append_index_html_on_directories(true);

    // 主路由
    Router::new()
        .nest("/api", api_routes)
        .fallback_service(static_files)
        .layer(
            ServiceBuilder::new()
                .layer(
                    CorsLayer::new()
                        .allow_origin(Any)
                        .allow_methods(Any)
                        .allow_headers(Any),
                )
        )
}

/// 启动 Web 服务器
pub async fn start_server(app: Router, port: u16) -> Result<(), Box<dyn std::error::Error>> {
    let addr = format!("0.0.0.0:{}", port);
    info!("🌐 Starting web server on http://{}", addr);
    
    let listener = tokio::net::TcpListener::bind(&addr).await?;
    
    info!("✅ Web server is running!");
    info!("📱 Open http://localhost:{} in your browser", port);
    
    axum::serve(listener, app).await?;
    
    Ok(())
}
