use crate::database::DatabaseRepository;
use crate::types::AgentConfig;
use crate::tools::ToolRegistry;
use std::sync::Arc;

/// Web 应用的共享状态
#[derive(Debug, Clone)]
pub struct AppState {
    pub db: DatabaseRepository,
    pub agent_config: AgentConfig,
    pub tool_registry: Arc<ToolRegistry>,
}

impl AppState {
    pub fn new(
        db: DatabaseRepository,
        agent_config: AgentConfig,
        tool_registry: Arc<ToolRegistry>,
    ) -> Self {
        Self {
            db,
            agent_config,
            tool_registry,
        }
    }
}
