use crate::database::models::*;
use crate::{AgentError, Result};
use chrono::Utc;
use sqlx::{Pool, Sqlite, SqlitePool};
use tracing::{debug, info};
use uuid::Uuid;

/// 数据库仓库，处理所有数据库操作
#[derive(Debug, Clone)]
pub struct DatabaseRepository {
    pool: Pool<Sqlite>,
}

impl DatabaseRepository {
    /// 创建新的数据库仓库
    pub async fn new(database_url: &str) -> Result<Self> {
        info!("Connecting to database: {}", database_url);
        
        let pool = SqlitePool::connect(database_url).await
            .map_err(|e| AgentError::other(format!("Failed to connect to database: {}", e)))?;

        let repo = Self { pool };
        repo.migrate().await?;
        
        Ok(repo)
    }

    /// 运行数据库迁移
    async fn migrate(&self) -> Result<()> {
        info!("Running database migrations");
        
        // 创建对话表
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS conversations (
                id TEXT PRIMARY KEY,
                title TEXT,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                metadata TEXT
            )
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AgentError::other(format!("Failed to create conversations table: {}", e)))?;

        // 创建消息表
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS messages (
                id TEXT PRIMARY KEY,
                conversation_id TEXT NOT NULL,
                role TEXT NOT NULL,
                content TEXT NOT NULL,
                timestamp DATETIME NOT NULL,
                tool_calls TEXT,
                tool_call_results TEXT,
                tool_call_id TEXT,
                FOREIGN KEY (conversation_id) REFERENCES conversations (id) ON DELETE CASCADE
            )
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AgentError::other(format!("Failed to create messages table: {}", e)))?;

        // 添加 tool_call_id 列（如果不存在）
        sqlx::query(
            r#"
            ALTER TABLE messages ADD COLUMN tool_call_id TEXT
            "#,
        )
        .execute(&self.pool)
        .await
        .ok(); // 忽略错误，因为列可能已经存在

        // 创建索引
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages (conversation_id)")
            .execute(&self.pool)
            .await
            .map_err(|e| AgentError::other(format!("Failed to create index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_messages_timestamp ON messages (timestamp)")
            .execute(&self.pool)
            .await
            .map_err(|e| AgentError::other(format!("Failed to create index: {}", e)))?;

        info!("Database migrations completed");
        Ok(())
    }

    /// 创建新对话
    pub async fn create_conversation(&self, title: Option<String>) -> Result<String> {
        let id = Uuid::new_v4().to_string();
        let now = Utc::now();
        
        debug!("Creating conversation with id: {}", id);
        
        sqlx::query(
            "INSERT INTO conversations (id, title, created_at, updated_at) VALUES (?, ?, ?, ?)"
        )
        .bind(&id)
        .bind(&title)
        .bind(now)
        .bind(now)
        .execute(&self.pool)
        .await
        .map_err(|e| AgentError::other(format!("Failed to create conversation: {}", e)))?;

        info!("Created conversation: {}", id);
        Ok(id)
    }

    /// 获取所有对话列表
    pub async fn get_conversations(&self) -> Result<Vec<ConversationInfo>> {
        debug!("Fetching all conversations");
        
        let conversations = sqlx::query_as::<_, DbConversation>(
            "SELECT * FROM conversations ORDER BY updated_at DESC"
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| AgentError::other(format!("Failed to fetch conversations: {}", e)))?;

        let mut result = Vec::new();
        
        for conv in conversations {
            // 获取消息数量和最后一条消息
            let message_count: i64 = sqlx::query_scalar(
                "SELECT COUNT(*) FROM messages WHERE conversation_id = ?"
            )
            .bind(&conv.id)
            .fetch_one(&self.pool)
            .await
            .unwrap_or(0);

            let last_message: Option<String> = sqlx::query_scalar(
                "SELECT content FROM messages WHERE conversation_id = ? ORDER BY timestamp DESC LIMIT 1"
            )
            .bind(&conv.id)
            .fetch_optional(&self.pool)
            .await
            .unwrap_or(None);

            result.push(ConversationInfo {
                id: conv.id,
                title: conv.title,
                created_at: conv.created_at,
                updated_at: conv.updated_at,
                message_count,
                last_message,
            });
        }

        debug!("Found {} conversations", result.len());
        Ok(result)
    }

    /// 获取对话详情
    pub async fn get_conversation(&self, conversation_id: &str) -> Result<Option<DbConversation>> {
        debug!("Fetching conversation: {}", conversation_id);
        
        let conversation = sqlx::query_as::<_, DbConversation>(
            "SELECT * FROM conversations WHERE id = ?"
        )
        .bind(conversation_id)
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| AgentError::other(format!("Failed to fetch conversation: {}", e)))?;

        Ok(conversation)
    }

    /// 获取对话的所有消息
    pub async fn get_messages(&self, conversation_id: &str) -> Result<Vec<WebMessage>> {
        debug!("Fetching messages for conversation: {}", conversation_id);
        
        let messages = sqlx::query_as::<_, DbMessage>(
            "SELECT * FROM messages WHERE conversation_id = ? ORDER BY timestamp ASC"
        )
        .bind(conversation_id)
        .fetch_all(&self.pool)
        .await
        .map_err(|e| AgentError::other(format!("Failed to fetch messages: {}", e)))?;

        let web_messages: Vec<WebMessage> = messages.into_iter().map(WebMessage::from).collect();
        debug!("Found {} messages", web_messages.len());
        
        Ok(web_messages)
    }

    /// 保存消息
    pub async fn save_message(&self, conversation_id: &str, message: &crate::types::Message) -> Result<()> {
        debug!("Saving message {} to conversation {}", message.id, conversation_id);
        
        let mut db_message = DbMessage::from(message.clone());
        db_message.conversation_id = conversation_id.to_string();

        sqlx::query(
            r#"
            INSERT INTO messages (id, conversation_id, role, content, timestamp, tool_calls, tool_call_results, tool_call_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            "#
        )
        .bind(&db_message.id)
        .bind(&db_message.conversation_id)
        .bind(&db_message.role)
        .bind(&db_message.content)
        .bind(&db_message.timestamp)
        .bind(&db_message.tool_calls)
        .bind(&db_message.tool_call_results)
        .bind(&db_message.tool_call_id)
        .execute(&self.pool)
        .await
        .map_err(|e| AgentError::other(format!("Failed to save message: {}", e)))?;

        // 更新对话的 updated_at 时间
        sqlx::query("UPDATE conversations SET updated_at = ? WHERE id = ?")
            .bind(Utc::now())
            .bind(conversation_id)
            .execute(&self.pool)
            .await
            .map_err(|e| AgentError::other(format!("Failed to update conversation timestamp: {}", e)))?;

        debug!("Message saved successfully");
        Ok(())
    }

    /// 删除对话
    pub async fn delete_conversation(&self, conversation_id: &str) -> Result<()> {
        debug!("Deleting conversation: {}", conversation_id);
        
        sqlx::query("DELETE FROM conversations WHERE id = ?")
            .bind(conversation_id)
            .execute(&self.pool)
            .await
            .map_err(|e| AgentError::other(format!("Failed to delete conversation: {}", e)))?;

        info!("Deleted conversation: {}", conversation_id);
        Ok(())
    }

    /// 更新对话标题
    pub async fn update_conversation_title(&self, conversation_id: &str, title: &str) -> Result<()> {
        debug!("Updating conversation title: {} -> {}", conversation_id, title);
        
        sqlx::query("UPDATE conversations SET title = ?, updated_at = ? WHERE id = ?")
            .bind(title)
            .bind(Utc::now())
            .bind(conversation_id)
            .execute(&self.pool)
            .await
            .map_err(|e| AgentError::other(format!("Failed to update conversation title: {}", e)))?;

        debug!("Conversation title updated");
        Ok(())
    }
}
