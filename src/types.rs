use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;

/// 消息类型，表示对话中的一条消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Message {
    pub id: Uuid,
    pub role: MessageRole,
    pub content: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub tool_calls: Option<Vec<ToolCall>>,
    pub tool_call_results: Option<Vec<ToolCallResult>>,
    /// 对于工具结果消息，这个字段存储对应的工具调用ID
    pub tool_call_id: Option<String>,
}

/// 消息角色
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum MessageRole {
    User,
    Assistant,
    System,
    Tool,
}

/// 工具调用请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolCall {
    pub id: String,
    pub name: String,
    pub parameters: serde_json::Value,
}

/// 工具调用结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolCallResult {
    pub tool_call_id: String,
    pub result: ToolExecutionResult,
}

/// 工具执行结果
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "status")]
pub enum ToolExecutionResult {
    Success { output: serde_json::Value },
    Error { error: String },
}

/// Agent 配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentConfig {
    pub llm: LlmConfig,
    pub max_iterations: usize,
    pub max_tool_calls_per_iteration: usize,
    pub system_prompt: Option<String>,
}

/// LLM 配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LlmConfig {
    pub provider: String,
    pub model: String,
    pub api_key: String,
    pub base_url: Option<String>,
    pub temperature: Option<f32>,
    pub max_tokens: Option<u32>,
}

/// Agent 状态
#[derive(Debug, Clone)]
pub enum AgentState {
    Idle,
    Processing,
    WaitingForToolResults,
    Error(String),
    Completed,
}

/// 对话会话
#[derive(Debug, Clone)]
pub struct Conversation {
    pub id: Uuid,
    pub messages: Vec<Message>,
    pub state: AgentState,
    pub metadata: HashMap<String, serde_json::Value>,
}

impl Default for AgentConfig {
    fn default() -> Self {
        Self {
            llm: LlmConfig {
                provider: "openrouter".to_string(),
                model: "anthropic/claude-3.5-sonnet".to_string(),
                api_key: "".to_string(),
                base_url: None,
                temperature: Some(0.7),
                max_tokens: Some(4096),
            },
            max_iterations: 10,
            max_tool_calls_per_iteration: 5,
            system_prompt: Some("You are a helpful AI assistant with access to tools.".to_string()),
        }
    }
}

impl Message {
    pub fn new_user(content: String) -> Self {
        Self {
            id: Uuid::new_v4(),
            role: MessageRole::User,
            content,
            timestamp: chrono::Utc::now(),
            tool_calls: None,
            tool_call_results: None,
            tool_call_id: None,
        }
    }

    pub fn new_assistant(content: String) -> Self {
        Self {
            id: Uuid::new_v4(),
            role: MessageRole::Assistant,
            content,
            timestamp: chrono::Utc::now(),
            tool_calls: None,
            tool_call_results: None,
            tool_call_id: None,
        }
    }

    pub fn new_system(content: String) -> Self {
        Self {
            id: Uuid::new_v4(),
            role: MessageRole::System,
            content,
            timestamp: chrono::Utc::now(),
            tool_calls: None,
            tool_call_results: None,
            tool_call_id: None,
        }
    }

    pub fn new_tool(content: String, tool_call_id: String) -> Self {
        Self {
            id: Uuid::new_v4(),
            role: MessageRole::Tool,
            content,
            timestamp: chrono::Utc::now(),
            tool_calls: None,
            tool_call_results: None,
            tool_call_id: Some(tool_call_id),
        }
    }
}

impl Conversation {
    pub fn new() -> Self {
        Self {
            id: Uuid::new_v4(),
            messages: Vec::new(),
            state: AgentState::Idle,
            metadata: HashMap::new(),
        }
    }

    pub fn add_message(&mut self, message: Message) {
        self.messages.push(message);
    }

    pub fn set_state(&mut self, state: AgentState) {
        self.state = state;
    }
}
