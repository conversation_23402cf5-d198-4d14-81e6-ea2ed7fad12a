use crate::Agent;
use crate::cli::commands::CommandHandler;
use indicatif::{ProgressBar, ProgressStyle};
use std::time::Duration;

/// CLI 用户界面管理器
pub struct CliUI {
    command_handler: CommandHandler,
}

impl CliUI {
    pub fn new() -> Self {
        Self {
            command_handler: CommandHandler::new(),
        }
    }

    /// 运行交互模式
    pub async fn run_interactive_mode(&self, agent: &mut Agent) -> Result<(), Box<dyn std::error::Error>> {
        // 显示欢迎信息
        let tools = agent.get_tool_registry().get_tool_names();
        self.command_handler.show_welcome(&tools);

        // 开始新对话
        let conversation_id = agent.start_conversation();
        tracing::info!("Started conversation: {}", conversation_id);

        loop {
            // 获取用户输入
            match self.command_handler.get_user_input() {
                Ok(input) => {
                    if input.is_empty() {
                        continue;
                    }

                    // 处理内置命令
                    let (is_builtin, should_continue) = self.command_handler.handle_builtin_command(&input, agent);

                    if !should_continue {
                        break; // 用户选择退出
                    }

                    // 如果不是内置命令，则发送给 Agent 处理
                    if !is_builtin {
                        self.process_agent_request(agent, &input).await;
                    }
                }
                Err(e) => {
                    self.command_handler.show_error(&format!("输入错误: {}", e));
                }
            }
        }

        Ok(())
    }

    /// 运行单次查询模式
    pub async fn run_single_query_mode(&self, agent: &mut Agent) -> Result<(), Box<dyn std::error::Error>> {
        println!("🤖 Minimal Agent - 单次查询模式");
        print!("请输入您的查询: ");
        std::io::Write::flush(&mut std::io::stdout())?;

        let mut input = String::new();
        std::io::stdin().read_line(&mut input)?;
        let input = input.trim();

        if input.is_empty() {
            println!("未提供输入。");
            return Ok(());
        }

        agent.start_conversation();
        self.process_agent_request(agent, input).await;

        Ok(())
    }

    /// 处理 Agent 请求
    async fn process_agent_request(&self, agent: &mut Agent, input: &str) {
        // 显示处理中状态
        let pb = self.create_progress_bar("正在处理您的请求...");
        pb.enable_steady_tick(Duration::from_millis(100));

        match agent.process_user_input(input.to_string()).await {
            Ok(response) => {
                pb.finish_and_clear();
                self.command_handler.show_response(&response);
            }
            Err(e) => {
                pb.finish_and_clear();
                self.command_handler.show_error(&format!("处理请求时出错: {}", e));
                tracing::error!("Error processing input: {}", e);
            }
        }
    }

    /// 创建进度条
    fn create_progress_bar(&self, message: &str) -> ProgressBar {
        let pb = ProgressBar::new_spinner();
        pb.set_style(
            ProgressStyle::default_spinner()
                .tick_strings(&["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"])
                .template("{spinner:.green} {msg}")
                .unwrap(),
        );
        pb.set_message(message.to_string());
        pb
    }


}

impl Default for CliUI {
    fn default() -> Self {
        Self::new()
    }
}
