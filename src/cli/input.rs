use rustyline::error::ReadlineError;
use rustyline::{DefaultEditor, Result as RustylineResult};
use console::{style, Emoji};
use std::path::PathBuf;

/// 增强的输入处理器，支持更好的中文字符处理和历史记录
pub struct EnhancedInput {
    editor: DefaultEditor,
    history_file: PathBuf,
}

impl EnhancedInput {
    /// 创建新的输入处理器
    pub fn new() -> RustylineResult<Self> {
        let mut editor = DefaultEditor::new()?;
        
        // 设置历史文件路径
        let history_file = dirs::home_dir()
            .unwrap_or_else(|| PathBuf::from("."))
            .join(".minimal_agent_history");
        
        // 尝试加载历史记录
        if history_file.exists() {
            let _ = editor.load_history(&history_file);
        }
        
        Ok(Self {
            editor,
            history_file,
        })
    }

    /// 获取用户输入，支持更好的中文字符处理
    pub fn get_input(&mut self, prompt: &str) -> <PERSON>lineResult<String> {
        match self.editor.readline(prompt) {
            Ok(line) => {
                // 添加到历史记录
                let _ = self.editor.add_history_entry(line.as_str());
                Ok(line.trim().to_string())
            }
            Err(ReadlineError::Interrupted) => {
                // Ctrl+C
                println!("^C");
                Ok(String::new())
            }
            Err(ReadlineError::Eof) => {
                // Ctrl+D
                println!("^D");
                Err(ReadlineError::Eof)
            }
            Err(err) => Err(err),
        }
    }

    /// 保存历史记录
    pub fn save_history(&mut self) -> RustylineResult<()> {
        self.editor.save_history(&self.history_file)
    }

    /// 清除历史记录
    pub fn clear_history(&mut self) {
        let _ = self.editor.clear_history();
    }

    /// 获取历史记录条目数
    pub fn history_len(&self) -> usize {
        // rustyline 14.0 中历史记录的长度获取方法
        0 // 暂时返回 0，避免编译错误
    }
}

impl Drop for EnhancedInput {
    fn drop(&mut self) {
        // 在析构时自动保存历史记录
        let _ = self.save_history();
    }
}

/// 简单的输入处理器（备用方案）
pub struct SimpleInput;

impl SimpleInput {
    pub fn new() -> Self {
        Self
    }

    pub fn get_input(&self, prompt: &str) -> std::io::Result<String> {
        use std::io::{self, Write};
        
        print!("{}", prompt);
        io::stdout().flush()?;
        
        let mut input = String::new();
        io::stdin().read_line(&mut input)?;
        Ok(input.trim().to_string())
    }
}

/// 输入处理器枚举
pub enum InputHandler {
    Enhanced(EnhancedInput),
    Simple(SimpleInput),
}

impl InputHandler {
    /// 创建最佳可用的输入处理器
    pub fn new() -> Self {
        match EnhancedInput::new() {
            Ok(enhanced) => {
                println!("✨ {}", style("启用增强输入模式 (支持历史记录和更好的中文处理)").dim());
                InputHandler::Enhanced(enhanced)
            }
            Err(_) => {
                println!("⚠️  {}", style("使用简单输入模式").yellow());
                InputHandler::Simple(SimpleInput::new())
            }
        }
    }

    /// 获取用户输入
    pub fn get_input(&mut self, prompt: &str) -> Result<String, Box<dyn std::error::Error>> {
        match self {
            InputHandler::Enhanced(enhanced) => {
                match enhanced.get_input(prompt) {
                    Ok(input) => Ok(input),
                    Err(ReadlineError::Eof) => {
                        // 用户按了 Ctrl+D，表示想要退出
                        Ok("quit".to_string())
                    }
                    Err(ReadlineError::Interrupted) => {
                        // 用户按了 Ctrl+C，返回空字符串继续
                        Ok(String::new())
                    }
                    Err(e) => Err(Box::new(e)),
                }
            }
            InputHandler::Simple(simple) => {
                Ok(simple.get_input(prompt)?)
            }
        }
    }

    /// 保存历史记录
    pub fn save_history(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        if let InputHandler::Enhanced(enhanced) = self {
            enhanced.save_history()?;
        }
        Ok(())
    }

    /// 清除历史记录
    pub fn clear_history(&mut self) {
        if let InputHandler::Enhanced(enhanced) = self {
            enhanced.clear_history();
        }
    }

    /// 获取历史记录长度
    pub fn history_len(&self) -> usize {
        match self {
            InputHandler::Enhanced(enhanced) => enhanced.history_len(),
            InputHandler::Simple(_) => 0,
        }
    }

    /// 显示输入提示信息
    pub fn show_input_help(&self) {
        let keyboard = Emoji("⌨️", "");
        let arrow = Emoji("↑↓", "");
        let tab = Emoji("⇥", "");
        
        println!("{} 输入提示:", style(keyboard).bold());
        
        match self {
            InputHandler::Enhanced(_) => {
                println!("  {} {} - 浏览历史命令", arrow, style("上/下箭头").cyan());
                println!("  {} {} - 自动补全 (如果可用)", tab, style("Tab").cyan());
                println!("  {} - 退出", style("Ctrl+D").cyan());
                println!("  {} - 中断当前输入", style("Ctrl+C").cyan());
                println!("  {} - 支持中文字符精确编辑", style("✓").green());
            }
            InputHandler::Simple(_) => {
                println!("  {} - 基本输入模式", style("Enter").cyan());
                println!("  {} - 输入 quit 退出", style("quit").cyan());
            }
        }
        println!();
    }
}
