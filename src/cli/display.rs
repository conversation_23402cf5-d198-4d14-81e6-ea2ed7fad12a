use console::{style, Emoji, Term};
use std::io::Write;

/// CLI 显示工具
pub struct CliDisplay {
    term: Term,
}

impl CliDisplay {
    pub fn new() -> Self {
        Self {
            term: Term::stdout(),
        }
    }

    /// 打印欢迎信息
    pub fn print_welcome(&self) {
        let robot = Emoji("🤖", "");
        let sparkles = Emoji("✨", "");
        
        println!();
        println!("{} {}", robot, style("Minimal Agent - 智能代码助手").bold().cyan());
        println!("{} {}", sparkles, style("基于 LLM 驱动的工具调用架构").dim());
        println!();
        self.print_separator();
    }

    /// 打印分隔线
    pub fn print_separator(&self) {
        println!("{}", style("─".repeat(60)).dim());
    }

    /// 打印状态信息
    pub fn print_status(&self, message: &str, status_type: StatusType) {
        let (emoji, styled_message) = match status_type {
            StatusType::Success => (Emoji("✅", "[OK]"), style(message).green()),
            StatusType::Error => (Emoji("❌", "[ERROR]"), style(message).red()),
            StatusType::Warning => (Emoji("⚠️", "[WARN]"), style(message).yellow()),
            StatusType::Info => (Emoji("ℹ️", "[INFO]"), style(message).blue()),
            StatusType::Processing => (Emoji("⏳", "[PROC]"), style(message).cyan()),
        };

        println!("{} {}", emoji, styled_message);
    }

    /// 打印工具列表
    pub fn print_tools(&self, tools: &[String]) {
        let tools_emoji = Emoji("🔧", "");
        println!("{} 可用工具:", style(tools_emoji).bold());
        
        for (i, tool) in tools.iter().enumerate() {
            let bullet = if i == tools.len() - 1 { "└─" } else { "├─" };
            println!("  {} {}", style(bullet).dim(), style(tool).green());
        }
        println!();
    }

    /// 打印帮助信息
    pub fn print_help(&self) {
        let book = Emoji("📚", "");
        let bulb = Emoji("💡", "");
        
        println!("{} 可用命令:", style(book).bold());
        println!("  {} - 显示此帮助信息", style("help").green());
        println!("  {} - 显示 Agent 状态", style("status").green());
        println!("  {} - 显示对话历史", style("history").green());
        println!("  {} - 清屏", style("clear").green());
        println!("  {} - 退出程序", style("quit/exit").green());
        println!();
        
        println!("{} 示例用法:", style(bulb).bold());
        println!("  • {}", style("echo hello world").cyan());
        println!("  • {}", style("calculate 15 + 25").cyan());
        println!("  • {}", style("read the file src/main.rs").cyan());
        println!("  • {}", style("create a new Python file with a hello world function").cyan());
        println!("  • {}", style("analyze the code structure of this project").cyan());
        println!("  • {}", style("search for files containing \"function\"").cyan());
        println!("  • {}", style("list all files in the src directory").cyan());
        println!();
    }

    /// 打印用户输入提示
    pub fn print_user_prompt(&self) {
        let user = Emoji("👤", "User:");
        print!("{} ", style(user).bold().blue());
        let _ = std::io::stdout().flush();
    }

    /// 打印 Agent 响应提示
    pub fn print_agent_prompt(&self) {
        let robot = Emoji("🤖", "Agent:");
        print!("{} ", style(robot).bold().green());
        let _ = std::io::stdout().flush();
    }

    /// 清屏
    pub fn clear_screen(&self) {
        let _ = self.term.clear_screen();
    }

    /// 打印错误信息
    pub fn print_error(&self, error: &str) {
        self.print_status(&format!("错误: {}", error), StatusType::Error);
    }

    /// 打印成功信息
    pub fn print_success(&self, message: &str) {
        self.print_status(message, StatusType::Success);
    }

    /// 打印信息
    pub fn print_info(&self, message: &str) {
        self.print_status(message, StatusType::Info);
    }

    /// 打印警告
    pub fn print_warning(&self, message: &str) {
        self.print_status(message, StatusType::Warning);
    }

    /// 打印处理中状态
    pub fn print_processing(&self, message: &str) {
        self.print_status(message, StatusType::Processing);
    }
}

impl Default for CliDisplay {
    fn default() -> Self {
        Self::new()
    }
}

/// 状态类型
#[derive(Debug, Clone, Copy)]
pub enum StatusType {
    Success,
    Error,
    Warning,
    Info,
    Processing,
}
