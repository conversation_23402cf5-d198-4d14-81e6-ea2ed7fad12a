use crate::Agent;
use crate::cli::display::CliDisplay;
use console::style;
use std::io;

/// CLI 命令处理器
pub struct CommandHandler {
    display: CliDisplay,
}

impl CommandHandler {
    pub fn new() -> Self {
        Self {
            display: CliDisplay::new(),
        }
    }

    /// 处理内置命令
    /// 返回值: (是否为内置命令, 是否应该继续运行)
    pub fn handle_builtin_command(&self, command: &str, agent: &Agent) -> (bool, bool) {
        match command.to_lowercase().trim() {
            "help" => {
                self.display.print_help();
                (true, true)
            }
            "status" => {
                self.print_agent_status(agent);
                (true, true)
            }
            "history" => {
                self.print_conversation_history(agent);
                (true, true)
            }
            "clear" => {
                self.display.clear_screen();
                self.display.print_welcome();
                (true, true)
            }
            "quit" | "exit" => {
                println!("👋 {}", style("再见！感谢使用 Minimal Agent").cyan());
                (true, false)
            }
            _ => (false, true),
        }
    }

    /// 打印 Agent 状态
    fn print_agent_status(&self, agent: &Agent) {
        println!();
        println!("{}", style("📊 Agent 状态").bold().cyan());
        self.display.print_separator();
        
        // 显示当前状态
        if let Some(state) = agent.get_current_state() {
            println!("  状态: {}", style(format!("{:?}", state)).green());
        } else {
            println!("  状态: {}", style("无活动对话").yellow());
        }
        
        // 显示可用工具
        let tools = agent.get_tool_registry().get_tool_names();
        println!("  工具数量: {}", style(tools.len()).green());
        
        // 显示工具列表
        if !tools.is_empty() {
            println!("  可用工具:");
            for (i, tool) in tools.iter().enumerate() {
                let bullet = if i == tools.len() - 1 { "    └─" } else { "    ├─" };
                println!("{} {}", style(bullet).dim(), style(tool).cyan());
            }
        }
        
        println!();
    }

    /// 打印对话历史
    fn print_conversation_history(&self, agent: &Agent) {
        println!();
        println!("{}", style("📜 对话历史").bold().cyan());
        self.display.print_separator();
        
        if let Some(messages) = agent.get_conversation_history() {
            if messages.is_empty() {
                println!("  {}", style("暂无对话历史").dim());
            } else {
                for (i, message) in messages.iter().enumerate() {
                    let (role_icon, role_style) = match message.role {
                        crate::MessageRole::User => ("👤", style(format!("{:?}", message.role)).blue()),
                        crate::MessageRole::Assistant => ("🤖", style(format!("{:?}", message.role)).green()),
                        crate::MessageRole::System => ("⚙️", style(format!("{:?}", message.role)).yellow()),
                        crate::MessageRole::Tool => ("🔧", style(format!("{:?}", message.role)).magenta()),
                    };

                    println!("  {}. {} {}: {}",
                        style(i + 1).dim(),
                        role_icon,
                        role_style,
                        self.truncate_message(&message.content, 100)
                    );
                }
            }
        } else {
            println!("  {}", style("无对话历史").dim());
        }
        
        println!();
    }

    /// 截断长消息
    fn truncate_message(&self, content: &str, max_len: usize) -> String {
        if content.len() <= max_len {
            content.to_string()
        } else {
            format!("{}...", &content[..max_len])
        }
    }

    /// 获取用户输入
    pub fn get_user_input(&self) -> io::Result<String> {
        self.display.print_user_prompt();
        
        let mut input = String::new();
        io::stdin().read_line(&mut input)?;
        Ok(input.trim().to_string())
    }

    /// 显示处理中状态
    pub fn show_processing(&self, message: &str) {
        self.display.print_processing(message);
    }

    /// 显示响应
    pub fn show_response(&self, response: &str) {
        self.display.print_agent_prompt();
        println!("{}", response);
        println!();
    }

    /// 显示错误
    pub fn show_error(&self, error: &str) {
        self.display.print_error(error);
        println!();
    }

    /// 显示欢迎信息
    pub fn show_welcome(&self, tools: &[String]) {
        self.display.print_welcome();
        
        println!("{}", style("交互模式已启动").green());
        println!("输入 {} 查看可用命令，输入 {} 退出", 
            style("help").cyan(), 
            style("quit").cyan()
        );
        
        self.display.print_tools(tools);
    }
}

impl Default for CommandHandler {
    fn default() -> Self {
        Self::new()
    }
}
