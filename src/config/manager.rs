use crate::config::{InteractiveConfig, ConfigCache, CachedConfig};
use crate::{AgentConfig, LlmConfig};
use console::style;
use dialoguer::Confirm;
use std::env;

/// 配置管理器
pub struct ConfigManager;

impl ConfigManager {
    /// 获取完整的 Agent 配置
    /// 优先级：命令行参数 > 环境变量 > 缓存配置 > 交互式输入
    pub async fn get_config(
        provider_arg: Option<String>,
        model_arg: Option<String>,
        api_key_arg: Option<String>,
        test_mode: bool,
        coding_mode: bool,
    ) -> Result<AgentConfig, Box<dyn std::error::Error>> {

        // 1. 尝试从命令行参数和环境变量获取配置
        let (provider, model, api_key) = Self::try_get_config_from_env(
            provider_arg,
            model_arg,
            api_key_arg,
            test_mode,
        );

        // 2. 如果没有完整配置，尝试从缓存加载
        let (final_provider, final_model, final_api_key, base_url, temperature, max_tokens) =
            if api_key.is_none() || api_key.as_ref().unwrap() == "your-api-key-here" {
                // 尝试从缓存加载配置
                if let Some(cached_config) = Self::try_load_cached_config()? {
                    // 使用缓存的配置
                    (
                        cached_config.provider,
                        cached_config.model,
                        cached_config.api_key,
                        cached_config.base_url,
                        cached_config.temperature,
                        cached_config.max_tokens,
                    )
                } else {
                    // 需要交互式配置
                    let interactive_config = Self::get_interactive_config(provider, model)?;

                    // 保存新配置到缓存
                    let cached_config = CachedConfig::new(
                        interactive_config.0.clone(),
                        interactive_config.1.clone(),
                        interactive_config.2.clone(),
                        interactive_config.3.clone(),
                        interactive_config.4,
                        interactive_config.5,
                    );

                    if let Err(e) = ConfigCache::save_config(&cached_config) {
                        eprintln!("⚠️  保存配置失败: {}", e);
                    }

                    interactive_config
                }
            } else {
                // 使用现有配置
                (
                    provider,
                    model,
                    api_key.unwrap(),
                    None,
                    Some(0.7),
                    Some(4096),
                )
            };
        
        // 创建 LLM 配置
        let llm_config = LlmConfig {
            provider: final_provider,
            model: final_model,
            api_key: final_api_key,
            base_url,
            temperature,
            max_tokens,
        };
        
        // 创建 Agent 配置
        let config = AgentConfig {
            llm: llm_config,
            max_iterations: 20,
            max_tool_calls_per_iteration: 10,
            system_prompt: Some(
                if coding_mode {
                    crate::prompts::get_system_prompt("coding").to_string()
                } else {
                    crate::prompts::get_system_prompt("general").to_string()
                }
            ),
        };
        
        Ok(config)
    }

    /// 尝试加载缓存的配置
    fn try_load_cached_config() -> Result<Option<CachedConfig>, Box<dyn std::error::Error>> {
        match ConfigCache::load_config() {
            Ok(Some(cached_config)) => {
                if ConfigCache::is_config_valid(&cached_config) {
                    // 显示缓存的配置并询问是否使用
                    ConfigCache::display_cached_config(&cached_config)?;

                    let use_cached = Confirm::new()
                        .with_prompt("是否使用已保存的配置？")
                        .default(true)
                        .interact()?;

                    if use_cached {
                        println!("✅ 使用缓存的配置");
                        return Ok(Some(cached_config));
                    } else {
                        println!("🔄 将重新配置...");
                        return Ok(None);
                    }
                } else {
                    println!("⚠️  缓存的配置无效，将重新配置");
                    return Ok(None);
                }
            }
            Ok(None) => {
                // 没有缓存配置
                return Ok(None);
            }
            Err(e) => {
                eprintln!("⚠️  读取缓存配置失败: {}", e);
                return Ok(None);
            }
        }
    }
    
    /// 尝试从环境变量和命令行参数获取配置
    fn try_get_config_from_env(
        provider_arg: Option<String>,
        model_arg: Option<String>,
        api_key_arg: Option<String>,
        test_mode: bool,
    ) -> (String, String, Option<String>) {
        
        // 确定提供商
        let provider = provider_arg.unwrap_or_else(|| "openrouter".to_string());
        
        // 确定模型
        let model = if test_mode {
            match provider.as_str() {
                "openai" => "gpt-3.5-turbo".to_string(),
                _ => "anthropic/claude-3-haiku".to_string(),
            }
        } else {
            model_arg.unwrap_or_else(|| {
                match provider.as_str() {
                    "openai" => "gpt-4-turbo".to_string(),
                    _ => "anthropic/claude-3.5-sonnet".to_string(),
                }
            })
        };
        
        // 尝试获取 API 密钥
        let api_key = api_key_arg
            .or_else(|| {
                match provider.as_str() {
                    "openrouter" => env::var("OPENROUTER_API_KEY").ok(),
                    "openai" => env::var("OPENAI_API_KEY").ok(),
                    _ => None,
                }
            });
        
        (provider, model, api_key)
    }
    
    /// 获取交互式配置
    fn get_interactive_config(
        _default_provider: String,
        _default_model: String,
    ) -> Result<(String, String, String, Option<String>, Option<f32>, Option<u32>), Box<dyn std::error::Error>> {
        
        println!();
        println!("{}", style("⚠️  未检测到有效的 API 密钥").yellow().bold());
        println!();
        println!("我们将通过交互式向导帮助您配置 Minimal Agent。");
        println!("您也可以按 Ctrl+C 退出，然后设置环境变量：");
        println!("  export OPENROUTER_API_KEY='your-key'");
        println!("  export OPENAI_API_KEY='your-key'");
        println!();
        
        // 显示提供商帮助信息
        InteractiveConfig::show_provider_help();
        
        // 收集交互式配置
        let config = InteractiveConfig::collect()?;
        
        Ok((
            config.provider,
            config.model,
            config.api_key,
            config.base_url,
            config.temperature,
            config.max_tokens,
        ))
    }
    
    /// 验证配置是否有效
    pub fn validate_config(config: &AgentConfig) -> Result<(), String> {
        // 检查 API 密钥
        if config.llm.api_key.is_empty() || config.llm.api_key == "your-api-key-here" {
            return Err("API 密钥无效".to_string());
        }
        
        // 检查提供商
        if !["openrouter", "openai"].contains(&config.llm.provider.as_str()) {
            return Err(format!("不支持的提供商: {}", config.llm.provider));
        }
        
        // 检查温度范围
        if let Some(temp) = config.llm.temperature {
            if temp < 0.0 || temp > 2.0 {
                return Err("温度必须在 0.0 到 2.0 之间".to_string());
            }
        }
        
        // 检查最大 token 数
        if let Some(tokens) = config.llm.max_tokens {
            if tokens == 0 || tokens > 100000 {
                return Err("最大 token 数必须在 1 到 100000 之间".to_string());
            }
        }
        
        Ok(())
    }
    
    /// 显示当前配置信息
    pub fn display_config(config: &AgentConfig) {
        println!();
        println!("{}", style("🔧 当前配置").bold().cyan());
        println!("────────────────────────────────────");
        println!("提供商: {}", style(&config.llm.provider).green());
        println!("模型: {}", style(&config.llm.model).green());
        
        let masked_key = if config.llm.api_key.len() > 8 {
            format!("{}***{}", 
                &config.llm.api_key[..4], 
                &config.llm.api_key[config.llm.api_key.len()-4..]
            )
        } else {
            "***".to_string()
        };
        println!("API 密钥: {}", style(masked_key).green());
        
        if let Some(temp) = config.llm.temperature {
            println!("温度: {}", style(temp).green());
        }
        
        if let Some(tokens) = config.llm.max_tokens {
            println!("最大 tokens: {}", style(tokens).green());
        }
        
        if let Some(url) = &config.llm.base_url {
            println!("API 端点: {}", style(url).green());
        }
        
        println!("────────────────────────────────────");
        println!();
    }

    /// 清除缓存的配置
    pub fn clear_cached_config() -> Result<(), Box<dyn std::error::Error>> {
        ConfigCache::clear_config()
    }

    /// 显示配置统计信息
    pub fn show_config_stats() -> Result<(), Box<dyn std::error::Error>> {
        ConfigCache::get_config_stats()
    }

    /// 备份当前配置
    pub fn backup_config() -> Result<(), Box<dyn std::error::Error>> {
        ConfigCache::backup_config()
    }

    /// 重新配置（清除缓存并重新设置）
    pub async fn reconfigure(
        test_mode: bool,
        coding_mode: bool,
    ) -> Result<AgentConfig, Box<dyn std::error::Error>> {
        println!("🔄 重新配置 Minimal Agent...");

        // 备份当前配置
        if let Err(e) = Self::backup_config() {
            eprintln!("⚠️  备份配置失败: {}", e);
        }

        // 清除缓存
        Self::clear_cached_config()?;

        // 重新获取配置（这会触发交互式配置）
        Self::get_config(None, None, None, test_mode, coding_mode).await
    }
}
