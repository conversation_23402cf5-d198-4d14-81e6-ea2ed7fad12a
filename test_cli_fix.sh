#!/bin/bash

# 测试 CLI 修复的脚本

echo "🧪 测试 CLI 持续对话功能"
echo "=========================="
echo ""

# 创建测试输入
cat > test_input.txt << 'EOF'
help
hi
status
hello world
history
quit
EOF

echo "📝 测试输入序列:"
echo "  1. help     (内置命令)"
echo "  2. hi       (非内置命令，应该尝试发送给 Agent)"
echo "  3. status   (内置命令)"
echo "  4. hello world (非内置命令，应该尝试发送给 Agent)"
echo "  5. history  (内置命令)"
echo "  6. quit     (退出命令)"
echo ""

echo "🚀 运行测试..."
echo ""

# 运行测试
cargo run -- --interactive --test-mode --coding-mode --api-key "test-key" < test_input.txt

echo ""
echo "✅ 测试完成！"
echo ""
echo "🔍 检查结果:"
echo "- 程序应该在每个非内置命令后显示错误信息但继续运行"
echo "- 程序应该在 quit 命令后正常退出"
echo "- 不应该在第一个非内置命令后就退出"

# 清理
rm -f test_input.txt
