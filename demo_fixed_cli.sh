#!/bin/bash

# 演示修复后的 CLI 持续对话功能

echo "🎉 CLI 持续对话功能演示"
echo "========================"
echo ""

echo "✅ 修复内容:"
echo "- 修复了发送第一条非内置命令后程序退出的问题"
echo "- 现在可以持续对话，即使 API 请求失败也会继续运行"
echo "- 内置命令和 Agent 请求可以混合使用"
echo ""

echo "📋 演示流程:"
echo "1. 显示帮助信息 (内置命令)"
echo "2. 尝试发送消息给 Agent (会因为无效 API 密钥而失败，但程序继续)"
echo "3. 查看状态 (内置命令)"
echo "4. 再次尝试发送消息 (同样会失败，但程序继续)"
echo "5. 查看对话历史 (内置命令)"
echo "6. 正常退出"
echo ""

# 创建演示输入
cat > demo_fixed_input.txt << 'EOF'
help
hello, can you help me?
status
what is 2 + 2?
history
quit
EOF

echo "🚀 开始演示..."
echo ""

# 运行演示
cargo run -- --interactive --test-mode --coding-mode --api-key "demo-key" < demo_fixed_input.txt

echo ""
echo "🎯 演示要点:"
echo "✅ 程序在 API 请求失败后继续运行"
echo "✅ 内置命令正常工作"
echo "✅ 可以查看对话历史"
echo "✅ 程序只在用户输入 quit 时退出"
echo ""

echo "💡 使用真实 API 密钥:"
echo "export OPENROUTER_API_KEY='your-real-key'"
echo "./start_cli.sh interactive"

# 清理
rm -f demo_fixed_input.txt
