#!/bin/bash

# Minimal Agent CLI 启动脚本

echo "🤖 Minimal Agent CLI 启动脚本"
echo "================================"

# 检查是否设置了 API 密钥
if [ -z "$OPENROUTER_API_KEY" ] && [ -z "$OPENAI_API_KEY" ]; then
    echo "⚠️  警告: 未检测到 API 密钥"
    echo "请设置以下环境变量之一:"
    echo "  export OPENROUTER_API_KEY='your-openrouter-key'"
    echo "  export OPENAI_API_KEY='your-openai-key'"
    echo ""
    echo "或者使用 --api-key 参数"
    echo ""
fi

# 解析命令行参数
MODE=${1:-interactive}
PROVIDER=${2:-openrouter}
MODEL=${3:-anthropic/claude-3.5-sonnet}

echo "🚀 启动 CLI 模式..."
echo "📍 模式: $MODE"
echo "🔧 提供商: $PROVIDER"
echo "🧠 模型: $MODEL"
echo "🧪 使用测试模式 (Claude-3 Haiku)"
echo ""

# 根据模式启动
if [ "$MODE" = "interactive" ] || [ "$MODE" = "i" ]; then
    echo "🎯 启动交互模式..."
    cargo run -- --interactive --provider "$PROVIDER" --model "$MODEL" --test-mode --coding-mode
elif [ "$MODE" = "single" ] || [ "$MODE" = "s" ]; then
    echo "🎯 启动单次查询模式..."
    cargo run -- --provider "$PROVIDER" --model "$MODEL" --test-mode --coding-mode
else
    echo "❌ 未知模式: $MODE"
    echo "可用模式:"
    echo "  interactive (i) - 交互模式"
    echo "  single (s)      - 单次查询模式"
    echo ""
    echo "用法: $0 [mode] [provider] [model]"
    echo "示例: $0 interactive openrouter anthropic/claude-3.5-sonnet"
    exit 1
fi

echo ""
echo "👋 CLI 已停止"
